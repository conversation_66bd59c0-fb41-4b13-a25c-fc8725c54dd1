{"name": "reviewerinator", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev"}, "keywords": [], "author": "", "license": "ISC", "description": "AI-powered Google Reviews analysis and reporting microsaas", "dependencies": {"@prisma/client": "^6.8.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.15.29", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "next": "^15.3.3", "postcss": "^8.5.4", "prisma": "^6.8.2", "puppeteer": "^24.9.0", "react": "^19.1.0", "react-dom": "^19.1.0", "recharts": "^2.15.3", "tailwindcss": "^4.1.8", "typescript": "^5.8.3"}}