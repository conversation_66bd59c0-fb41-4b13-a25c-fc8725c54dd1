// Simple test script to verify the application is working
const axios = require('axios');

async function testApp() {
  try {
    console.log('Testing Reviewerinator application...');
    
    // Test 1: Check if the main page loads
    console.log('\n1. Testing main page...');
    const homeResponse = await axios.get('http://localhost:3000');
    if (homeResponse.status === 200) {
      console.log('✅ Main page loads successfully');
    } else {
      console.log('❌ Main page failed to load');
      return;
    }

    // Test 2: Check if dashboard loads
    console.log('\n2. Testing dashboard...');
    const dashboardResponse = await axios.get('http://localhost:3000/dashboard');
    if (dashboardResponse.status === 200) {
      console.log('✅ Dashboard loads successfully');
    } else {
      console.log('❌ Dashboard failed to load');
    }

    // Test 3: Test API endpoint (without actually scraping)
    console.log('\n3. Testing API endpoint...');
    try {
      const apiResponse = await axios.post('http://localhost:3000/api/scrape-reviews', {
        googleUrl: 'invalid-url'
      });
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ API endpoint responds correctly to invalid input');
      } else {
        console.log('❌ API endpoint error:', error.message);
      }
    }

    console.log('\n🎉 Application test completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Add your DeepSeek API key to the .env file');
    console.log('2. Test with a real Google Maps URL');
    console.log('3. The application is ready to use!');

  } catch (error) {
    console.log('❌ Test failed:', error.message);
    console.log('Make sure the development server is running with: npm run dev');
  }
}

testApp();
