import puppeteer from 'puppeteer';
import { ScrapingResult } from '@/types';

export async function scrapeGoogleReviews(googleUrl: string): Promise<ScrapingResult> {
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
  });

  try {
    const page = await browser.newPage();
    await page.setUserAgent(
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    );

    // Navigate to the Google Maps URL
    await page.goto(googleUrl, { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Extract business information
    const businessInfo = await page.evaluate(() => {
      const nameElement = document.querySelector('h1[data-attrid="title"]') ||
                          document.querySelector('h1') ||
                          document.querySelector('[data-attrid="title"]');

      const ratingElement = document.querySelector('[data-attrid="kc:/location/location:star_rating"] span') ||
                           document.querySelector('.Aq14fc');

      const addressElement = document.querySelector('[data-attrid="kc:/location/location:address"] span') ||
                            document.querySelector('.LrzXr');

      const reviewCountElement = document.querySelector('[data-attrid="kc:/location/location:num_reviews"] span') ||
                                document.querySelector('.hqzQac span');

      return {
        name: nameElement?.textContent?.trim() || 'Unknown Business',
        rating: ratingElement ? parseFloat(ratingElement.textContent?.replace(',', '.') || '0') : 0,
        address: addressElement?.textContent?.trim() || '',
        totalReviews: reviewCountElement ?
          parseInt(reviewCountElement.textContent?.replace(/[^\d]/g, '') || '0') : 0,
      };
    });

    // Try to find and click the reviews section
    try {
      await page.click('[data-tab-index="1"]', { timeout: 5000 });
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      console.log('Could not find reviews tab, trying alternative selectors');
    }

    // Scroll to load more reviews
    await autoScroll(page);

    // Extract reviews
    const reviews = await page.evaluate(() => {
      const reviewElements = document.querySelectorAll('[data-review-id], .jftiEf, .MyEned');
      const extractedReviews: any[] = [];

      reviewElements.forEach((element) => {
        try {
          const authorElement = element.querySelector('.d4r55') ||
                               element.querySelector('.X43Kjb');

          const ratingElement = element.querySelector('[aria-label*="star"]') ||
                               element.querySelector('.kvMYJc');

          const textElement = element.querySelector('.wiI7pd') ||
                             element.querySelector('.MyEned');

          const dateElement = element.querySelector('.rsqaWe') ||
                             element.querySelector('.p2TkOb');

          if (authorElement && ratingElement) {
            const ratingText = ratingElement.getAttribute('aria-label') ||
                              ratingElement.textContent || '';
            const rating = parseInt(ratingText.match(/(\d+)/)?.[1] || '0');

            const dateText = dateElement?.textContent?.trim() || '';
            let reviewDate = new Date();

            // Parse relative dates like "2 months ago", "1 week ago"
            if (dateText.includes('month')) {
              const months = parseInt(dateText.match(/(\d+)/)?.[1] || '1');
              reviewDate.setMonth(reviewDate.getMonth() - months);
            } else if (dateText.includes('week')) {
              const weeks = parseInt(dateText.match(/(\d+)/)?.[1] || '1');
              reviewDate.setDate(reviewDate.getDate() - (weeks * 7));
            } else if (dateText.includes('day')) {
              const days = parseInt(dateText.match(/(\d+)/)?.[1] || '1');
              reviewDate.setDate(reviewDate.getDate() - days);
            }

            extractedReviews.push({
              authorName: authorElement.textContent?.trim() || 'Anonymous',
              rating: rating || 5,
              text: textElement?.textContent?.trim() || null,
              date: reviewDate.toISOString(),
            });
          }
        } catch (error) {
          console.log('Error extracting review:', error);
        }
      });

      return extractedReviews;
    });

    return {
      business: businessInfo,
      reviews: reviews.map(review => ({
        ...review,
        date: new Date(review.date),
      })),
    };

  } finally {
    await browser.close();
  }
}

async function autoScroll(page: any) {
  await page.evaluate(async () => {
    await new Promise<void>((resolve) => {
      let totalHeight = 0;
      const distance = 100;
      const timer = setInterval(() => {
        const scrollHeight = document.body.scrollHeight;
        window.scrollBy(0, distance);
        totalHeight += distance;

        if (totalHeight >= scrollHeight || totalHeight > 5000) {
          clearInterval(timer);
          resolve();
        }
      }, 100);
    });
  });
}
