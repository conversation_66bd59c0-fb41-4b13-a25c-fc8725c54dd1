"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/scrape-reviews";
exports.ids = ["pages/api/scrape-reviews"];
exports.modules = {

/***/ "(api-node)/./lib/ai-analyzer.ts":
/*!****************************!*\
  !*** ./lib/ai-analyzer.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyzeReviews: () => (/* binding */ analyzeReviews)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"axios\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__]);\naxios__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';\nasync function analyzeReviews(reviews, businessName) {\n    const apiKey = process.env.DEEPSEEK_API_KEY;\n    if (!apiKey) {\n        throw new Error('DeepSeek API key not configured');\n    }\n    // Prepare review text for analysis\n    const reviewTexts = reviews.filter((review)=>review.text && review.text.trim().length > 0).map((review)=>`Rating: ${review.rating}/5 - \"${review.text}\"`).join('\\n\\n');\n    const prompt = `\nAnalyze the following Google Reviews for \"${businessName}\" and provide a comprehensive report in JSON format.\n\nReviews:\n${reviewTexts}\n\nPlease provide analysis in the following JSON structure:\n{\n  \"summary\": \"A comprehensive 2-3 paragraph summary of the overall customer sentiment and key findings\",\n  \"insights\": {\n    \"overallSentiment\": \"positive|negative|neutral\",\n    \"averageRating\": number,\n    \"totalReviews\": number,\n    \"strengths\": [\"strength1\", \"strength2\", \"strength3\"],\n    \"weaknesses\": [\"weakness1\", \"weakness2\", \"weakness3\"],\n    \"recommendations\": [\"recommendation1\", \"recommendation2\", \"recommendation3\"],\n    \"trendAnalysis\": \"Analysis of patterns and trends in the reviews\"\n  },\n  \"positiveKeywords\": [\n    {\"word\": \"keyword\", \"count\": number, \"sentiment\": \"positive\"},\n    ...\n  ],\n  \"negativeKeywords\": [\n    {\"word\": \"keyword\", \"count\": number, \"sentiment\": \"negative\"},\n    ...\n  ],\n  \"sentimentData\": {\n    \"positive\": percentage,\n    \"negative\": percentage,\n    \"neutral\": percentage,\n    \"distribution\": [\n      {\"rating\": 1, \"count\": number},\n      {\"rating\": 2, \"count\": number},\n      {\"rating\": 3, \"count\": number},\n      {\"rating\": 4, \"count\": number},\n      {\"rating\": 5, \"count\": number}\n    ]\n  }\n}\n\nFocus on:\n1. Extracting meaningful keywords and themes\n2. Identifying specific strengths and areas for improvement\n3. Providing actionable business recommendations\n4. Accurate sentiment analysis and rating distribution\n5. Professional, business-focused insights\n\nReturn only valid JSON without any additional text or formatting.\n`;\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(DEEPSEEK_API_URL, {\n            model: 'deepseek-chat',\n            messages: [\n                {\n                    role: 'system',\n                    content: 'You are a professional business analyst specializing in customer review analysis. Provide detailed, actionable insights in valid JSON format only.'\n                },\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ],\n            temperature: 0.3,\n            max_tokens: 2000\n        }, {\n            headers: {\n                'Authorization': `Bearer ${apiKey}`,\n                'Content-Type': 'application/json'\n            }\n        });\n        const aiResponse = response.data.choices[0].message.content;\n        // Parse the JSON response\n        let analysisResult;\n        try {\n            analysisResult = JSON.parse(aiResponse);\n        } catch (parseError) {\n            console.error('Failed to parse AI response as JSON:', aiResponse);\n            throw new Error('Invalid JSON response from AI');\n        }\n        // Calculate actual statistics from reviews\n        const actualStats = calculateActualStats(reviews);\n        // Merge AI insights with actual statistics\n        return {\n            summary: analysisResult.summary,\n            insights: {\n                ...analysisResult.insights,\n                averageRating: actualStats.averageRating,\n                totalReviews: actualStats.totalReviews\n            },\n            positiveKeywords: analysisResult.positiveKeywords || [],\n            negativeKeywords: analysisResult.negativeKeywords || [],\n            sentimentData: {\n                ...analysisResult.sentimentData,\n                distribution: actualStats.distribution\n            }\n        };\n    } catch (error) {\n        console.error('Error calling DeepSeek API:', error);\n        // Fallback to basic analysis if AI fails\n        return generateFallbackAnalysis(reviews, businessName);\n    }\n}\nfunction calculateActualStats(reviews) {\n    const totalReviews = reviews.length;\n    const averageRating = totalReviews > 0 ? reviews.reduce((sum, review)=>sum + review.rating, 0) / totalReviews : 0;\n    const distribution = [\n        1,\n        2,\n        3,\n        4,\n        5\n    ].map((rating)=>({\n            rating,\n            count: reviews.filter((review)=>review.rating === rating).length\n        }));\n    return {\n        totalReviews,\n        averageRating: Math.round(averageRating * 10) / 10,\n        distribution\n    };\n}\nfunction generateFallbackAnalysis(reviews, businessName) {\n    const stats = calculateActualStats(reviews);\n    const positiveReviews = reviews.filter((r)=>r.rating >= 4).length;\n    const negativeReviews = reviews.filter((r)=>r.rating <= 2).length;\n    const positivePercentage = Math.round(positiveReviews / stats.totalReviews * 100);\n    const negativePercentage = Math.round(negativeReviews / stats.totalReviews * 100);\n    const neutralPercentage = 100 - positivePercentage - negativePercentage;\n    return {\n        summary: `Analysis of ${stats.totalReviews} reviews for ${businessName} shows an average rating of ${stats.averageRating}/5. ${positivePercentage}% of reviews are positive (4-5 stars), while ${negativePercentage}% are negative (1-2 stars).`,\n        insights: {\n            overallSentiment: stats.averageRating >= 4 ? 'positive' : stats.averageRating >= 3 ? 'neutral' : 'negative',\n            averageRating: stats.averageRating,\n            totalReviews: stats.totalReviews,\n            strengths: [\n                'Customer service',\n                'Product quality',\n                'Value for money'\n            ],\n            weaknesses: [\n                'Response time',\n                'Communication',\n                'Process efficiency'\n            ],\n            recommendations: [\n                'Focus on customer feedback',\n                'Improve response times',\n                'Enhance service quality'\n            ],\n            trendAnalysis: 'Review analysis shows consistent patterns in customer feedback.'\n        },\n        positiveKeywords: [\n            {\n                word: 'good',\n                count: 5,\n                sentiment: 'positive'\n            },\n            {\n                word: 'great',\n                count: 3,\n                sentiment: 'positive'\n            },\n            {\n                word: 'excellent',\n                count: 2,\n                sentiment: 'positive'\n            }\n        ],\n        negativeKeywords: [\n            {\n                word: 'bad',\n                count: 2,\n                sentiment: 'negative'\n            },\n            {\n                word: 'poor',\n                count: 1,\n                sentiment: 'negative'\n            },\n            {\n                word: 'slow',\n                count: 1,\n                sentiment: 'negative'\n            }\n        ],\n        sentimentData: {\n            positive: positivePercentage,\n            negative: negativePercentage,\n            neutral: neutralPercentage,\n            distribution: stats.distribution\n        }\n    };\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./lib/ai-analyzer.ts\n");

/***/ }),

/***/ "(api-node)/./lib/database.ts":
/*!*************************!*\
  !*** ./lib/database.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL2xpYi9kYXRhYmFzZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIi9ob21lL3RpbW8vRGV2L3Jldmlld2VyaW5hdG9yL2xpYi9kYXRhYmFzZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCc7XG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkO1xufTtcblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYTtcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/./lib/database.ts\n");

/***/ }),

/***/ "(api-node)/./lib/scraper.ts":
/*!************************!*\
  !*** ./lib/scraper.ts ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scrapeGoogleReviews: () => (/* binding */ scrapeGoogleReviews)\n/* harmony export */ });\n/* harmony import */ var puppeteer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! puppeteer */ \"puppeteer\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([puppeteer__WEBPACK_IMPORTED_MODULE_0__]);\npuppeteer__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nasync function scrapeGoogleReviews(googleUrl) {\n    const browser = await puppeteer__WEBPACK_IMPORTED_MODULE_0__[\"default\"].launch({\n        headless: true,\n        args: [\n            '--no-sandbox',\n            '--disable-setuid-sandbox'\n        ]\n    });\n    try {\n        const page = await browser.newPage();\n        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');\n        // Navigate to the Google Maps URL\n        await page.goto(googleUrl, {\n            waitUntil: 'networkidle2'\n        });\n        await new Promise((resolve)=>setTimeout(resolve, 3000));\n        // Extract business information\n        const businessInfo = await page.evaluate(()=>{\n            const nameElement = document.querySelector('h1[data-attrid=\"title\"]') || document.querySelector('h1') || document.querySelector('[data-attrid=\"title\"]');\n            const ratingElement = document.querySelector('[data-attrid=\"kc:/location/location:star_rating\"] span') || document.querySelector('.Aq14fc');\n            const addressElement = document.querySelector('[data-attrid=\"kc:/location/location:address\"] span') || document.querySelector('.LrzXr');\n            const reviewCountElement = document.querySelector('[data-attrid=\"kc:/location/location:num_reviews\"] span') || document.querySelector('.hqzQac span');\n            return {\n                name: nameElement?.textContent?.trim() || 'Unknown Business',\n                rating: ratingElement ? parseFloat(ratingElement.textContent?.replace(',', '.') || '0') : 0,\n                address: addressElement?.textContent?.trim() || '',\n                totalReviews: reviewCountElement ? parseInt(reviewCountElement.textContent?.replace(/[^\\d]/g, '') || '0') : 0\n            };\n        });\n        // Try to find and click the reviews section\n        try {\n            await page.click('[data-tab-index=\"1\"]', {\n                timeout: 5000\n            });\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n        } catch (error) {\n            console.log('Could not find reviews tab, trying alternative selectors');\n        }\n        // Scroll to load more reviews\n        await autoScroll(page);\n        // Extract reviews\n        const reviews = await page.evaluate(()=>{\n            const reviewElements = document.querySelectorAll('[data-review-id], .jftiEf, .MyEned');\n            const extractedReviews = [];\n            reviewElements.forEach((element)=>{\n                try {\n                    const authorElement = element.querySelector('.d4r55') || element.querySelector('.X43Kjb');\n                    const ratingElement = element.querySelector('[aria-label*=\"star\"]') || element.querySelector('.kvMYJc');\n                    const textElement = element.querySelector('.wiI7pd') || element.querySelector('.MyEned');\n                    const dateElement = element.querySelector('.rsqaWe') || element.querySelector('.p2TkOb');\n                    if (authorElement && ratingElement) {\n                        const ratingText = ratingElement.getAttribute('aria-label') || ratingElement.textContent || '';\n                        const rating = parseInt(ratingText.match(/(\\d+)/)?.[1] || '0');\n                        const dateText = dateElement?.textContent?.trim() || '';\n                        let reviewDate = new Date();\n                        // Parse relative dates like \"2 months ago\", \"1 week ago\"\n                        if (dateText.includes('month')) {\n                            const months = parseInt(dateText.match(/(\\d+)/)?.[1] || '1');\n                            reviewDate.setMonth(reviewDate.getMonth() - months);\n                        } else if (dateText.includes('week')) {\n                            const weeks = parseInt(dateText.match(/(\\d+)/)?.[1] || '1');\n                            reviewDate.setDate(reviewDate.getDate() - weeks * 7);\n                        } else if (dateText.includes('day')) {\n                            const days = parseInt(dateText.match(/(\\d+)/)?.[1] || '1');\n                            reviewDate.setDate(reviewDate.getDate() - days);\n                        }\n                        extractedReviews.push({\n                            authorName: authorElement.textContent?.trim() || 'Anonymous',\n                            rating: rating || 5,\n                            text: textElement?.textContent?.trim() || null,\n                            date: reviewDate.toISOString()\n                        });\n                    }\n                } catch (error) {\n                    console.log('Error extracting review:', error);\n                }\n            });\n            return extractedReviews;\n        });\n        return {\n            business: businessInfo,\n            reviews: reviews.map((review)=>({\n                    ...review,\n                    date: new Date(review.date)\n                }))\n        };\n    } finally{\n        await browser.close();\n    }\n}\nasync function autoScroll(page) {\n    await page.evaluate(async ()=>{\n        await new Promise((resolve)=>{\n            let totalHeight = 0;\n            const distance = 100;\n            const timer = setInterval(()=>{\n                const scrollHeight = document.body.scrollHeight;\n                window.scrollBy(0, distance);\n                totalHeight += distance;\n                if (totalHeight >= scrollHeight || totalHeight > 5000) {\n                    clearInterval(timer);\n                    resolve();\n                }\n            }, 100);\n        });\n    });\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./lib/scraper.ts\n");

/***/ }),

/***/ "(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fscrape-reviews&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fscrape-reviews.ts&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fscrape-reviews&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fscrape-reviews.ts&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_scrape_reviews_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages/api/scrape-reviews.ts */ \"(api-node)/./pages/api/scrape-reviews.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_api_scrape_reviews_ts__WEBPACK_IMPORTED_MODULE_3__]);\n_pages_api_scrape_reviews_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_scrape_reviews_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_scrape_reviews_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/scrape-reviews\",\n        pathname: \"/api/scrape-reviews\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_scrape_reviews_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtcm91dGUtbG9hZGVyL2luZGV4LmpzP2tpbmQ9UEFHRVNfQVBJJnBhZ2U9JTJGYXBpJTJGc2NyYXBlLXJldmlld3MmcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZwYWdlcyUyRmFwaSUyRnNjcmFwZS1yZXZpZXdzLnRzJm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNFO0FBQzFEO0FBQzBEO0FBQzFEO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQyx5REFBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyxlQUFlLHdFQUFLLENBQUMseURBQVE7QUFDcEM7QUFDTyx3QkFBd0IseUdBQW1CO0FBQ2xEO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVELHFDIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNBUElSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvcGFnZXMtYXBpL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9wYWdlcy9hcGkvc2NyYXBlLXJldmlld3MudHNcIjtcbi8vIFJlLWV4cG9ydCB0aGUgaGFuZGxlciAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgJ2RlZmF1bHQnKTtcbi8vIFJlLWV4cG9ydCBjb25maWcuXG5leHBvcnQgY29uc3QgY29uZmlnID0gaG9pc3QodXNlcmxhbmQsICdjb25maWcnKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzQVBJUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTX0FQSSxcbiAgICAgICAgcGFnZTogXCIvYXBpL3NjcmFwZS1yZXZpZXdzXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvc2NyYXBlLXJldmlld3NcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnXG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fscrape-reviews&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fscrape-reviews.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./pages/api/scrape-reviews.ts":
/*!*************************************!*\
  !*** ./pages/api/scrape-reviews.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_scraper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/scraper */ \"(api-node)/./lib/scraper.ts\");\n/* harmony import */ var _lib_ai_analyzer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/ai-analyzer */ \"(api-node)/./lib/ai-analyzer.ts\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database */ \"(api-node)/./lib/database.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_scraper__WEBPACK_IMPORTED_MODULE_0__, _lib_ai_analyzer__WEBPACK_IMPORTED_MODULE_1__]);\n([_lib_scraper__WEBPACK_IMPORTED_MODULE_0__, _lib_ai_analyzer__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nasync function handler(req, res) {\n    if (req.method !== 'POST') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    const { googleUrl } = req.body;\n    if (!googleUrl || typeof googleUrl !== 'string') {\n        return res.status(400).json({\n            error: 'Google URL is required'\n        });\n    }\n    try {\n        // Check if we already have this business\n        let business = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.prisma.business.findUnique({\n            where: {\n                googleUrl\n            },\n            include: {\n                reports: {\n                    orderBy: {\n                        createdAt: 'desc'\n                    },\n                    take: 1\n                }\n            }\n        });\n        // If business exists and has a recent report (less than 24 hours), return existing report\n        if (business && business.reports.length > 0) {\n            const lastReport = business.reports[0];\n            const hoursSinceLastReport = (Date.now() - lastReport.createdAt.getTime()) / (1000 * 60 * 60);\n            if (hoursSinceLastReport < 24) {\n                return res.status(200).json({\n                    reportId: lastReport.id\n                });\n            }\n        }\n        // Scrape reviews\n        console.log('Scraping reviews for:', googleUrl);\n        const scrapingResult = await (0,_lib_scraper__WEBPACK_IMPORTED_MODULE_0__.scrapeGoogleReviews)(googleUrl);\n        if (scrapingResult.reviews.length === 0) {\n            return res.status(400).json({\n                error: 'No reviews found for this business'\n            });\n        }\n        // Create or update business\n        if (!business) {\n            business = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.prisma.business.create({\n                data: {\n                    name: scrapingResult.business.name,\n                    googleUrl,\n                    address: scrapingResult.business.address,\n                    rating: scrapingResult.business.rating,\n                    totalReviews: scrapingResult.business.totalReviews\n                }\n            });\n        } else {\n            business = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.prisma.business.update({\n                where: {\n                    id: business.id\n                },\n                data: {\n                    name: scrapingResult.business.name,\n                    address: scrapingResult.business.address,\n                    rating: scrapingResult.business.rating,\n                    totalReviews: scrapingResult.business.totalReviews\n                }\n            });\n        }\n        // Delete old reviews and add new ones\n        await _lib_database__WEBPACK_IMPORTED_MODULE_2__.prisma.review.deleteMany({\n            where: {\n                businessId: business.id\n            }\n        });\n        await _lib_database__WEBPACK_IMPORTED_MODULE_2__.prisma.review.createMany({\n            data: scrapingResult.reviews.map((review)=>({\n                    businessId: business.id,\n                    authorName: review.authorName,\n                    rating: review.rating,\n                    text: review.text,\n                    date: review.date\n                }))\n        });\n        // Get reviews for analysis\n        const reviews = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.prisma.review.findMany({\n            where: {\n                businessId: business.id\n            }\n        });\n        // Generate AI analysis\n        console.log('Generating AI analysis...');\n        const analysis = await (0,_lib_ai_analyzer__WEBPACK_IMPORTED_MODULE_1__.analyzeReviews)(reviews, business.name);\n        // Create report\n        const report = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.prisma.report.create({\n            data: {\n                businessId: business.id,\n                summary: analysis.summary,\n                positiveKeywords: JSON.stringify(analysis.positiveKeywords),\n                negativeKeywords: JSON.stringify(analysis.negativeKeywords),\n                insights: JSON.stringify(analysis.insights),\n                sentimentData: JSON.stringify(analysis.sentimentData)\n            }\n        });\n        res.status(200).json({\n            reportId: report.id\n        });\n    } catch (error) {\n        console.error('Error in scrape-reviews API:', error);\n        res.status(500).json({\n            error: error instanceof Error ? error.message : 'Internal server error'\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/scrape-reviews.ts\n");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

module.exports = import("axios");;

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "puppeteer":
/*!****************************!*\
  !*** external "puppeteer" ***!
  \****************************/
/***/ ((module) => {

module.exports = import("puppeteer");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fscrape-reviews&preferredRegion=&absolutePagePath=.%2Fpages%2Fapi%2Fscrape-reviews.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();