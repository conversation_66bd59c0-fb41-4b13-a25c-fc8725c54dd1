/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/get-domain-locale.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/client/get-domain-locale.js ***!
  \************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getDomainLocale\", ({\n    enumerable: true,\n    get: function() {\n        return getDomainLocale;\n    }\n}));\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"(pages-dir-browser)/./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst basePath =  false || '';\nfunction getDomainLocale(path, locale, locales, domainLocales) {\n    if (false) {} else {\n        return false;\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-domain-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/get-domain-locale.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/link.js":
/*!***********************************************!*\
  !*** ./node_modules/next/dist/client/link.js ***!
  \***********************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return _default;\n    },\n    useLinkStatus: function() {\n        return useLinkStatus;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _resolvehref = __webpack_require__(/*! ./resolve-href */ \"(pages-dir-browser)/./node_modules/next/dist/client/resolve-href.js\");\nconst _islocalurl = __webpack_require__(/*! ../shared/lib/router/utils/is-local-url */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _formaturl = __webpack_require__(/*! ../shared/lib/router/utils/format-url */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _addlocale = __webpack_require__(/*! ./add-locale */ \"(pages-dir-browser)/./node_modules/next/dist/client/add-locale.js\");\nconst _routercontextsharedruntime = __webpack_require__(/*! ../shared/lib/router-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router-context.shared-runtime.js\");\nconst _useintersection = __webpack_require__(/*! ./use-intersection */ \"(pages-dir-browser)/./node_modules/next/dist/client/use-intersection.js\");\nconst _getdomainlocale = __webpack_require__(/*! ./get-domain-locale */ \"(pages-dir-browser)/./node_modules/next/dist/client/get-domain-locale.js\");\nconst _addbasepath = __webpack_require__(/*! ./add-base-path */ \"(pages-dir-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _usemergedref = __webpack_require__(/*! ./use-merged-ref */ \"(pages-dir-browser)/./node_modules/next/dist/client/use-merged-ref.js\");\nconst _erroronce = __webpack_require__(/*! ../shared/lib/utils/error-once */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\");\nconst prefetched = new Set();\nfunction prefetch(router, href, as, options) {\n    if (false) {}\n    if (!(0, _islocalurl.isLocalURL)(href)) {\n        return;\n    }\n    // We should only dedupe requests when experimental.optimisticClientCache is\n    // disabled.\n    if (!options.bypassPrefetchedCheck) {\n        const locale = typeof options.locale !== 'undefined' ? options.locale : 'locale' in router ? router.locale : undefined;\n        const prefetchedKey = href + '%' + as + '%' + locale;\n        // If we've already fetched the key, then don't prefetch it again!\n        if (prefetched.has(prefetchedKey)) {\n            return;\n        }\n        // Mark this URL as prefetched.\n        prefetched.add(prefetchedKey);\n    }\n    // Prefetch the JSON page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    router.prefetch(href, as, options).catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute('target');\n    return target && target !== '_self' || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, router, href, as, replace, shallow, scroll, locale, onNavigate) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === 'A';\n    if (isAnchorNodeName && isModifiedEvent(e) || e.currentTarget.hasAttribute('download')) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    if (!(0, _islocalurl.isLocalURL)(href)) {\n        if (replace) {\n            // browser default behavior does not replace the history state\n            // so we need to do it manually\n            e.preventDefault();\n            location.replace(href);\n        }\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        if (onNavigate) {\n            let isDefaultPrevented = false;\n            onNavigate({\n                preventDefault: ()=>{\n                    isDefaultPrevented = true;\n                }\n            });\n            if (isDefaultPrevented) {\n                return;\n            }\n        }\n        // If the router is an NextRouter instance it will have `beforePopState`\n        const routerScroll = scroll != null ? scroll : true;\n        if ('beforePopState' in router) {\n            router[replace ? 'replace' : 'push'](href, as, {\n                shallow,\n                locale,\n                scroll: routerScroll\n            });\n        } else {\n            router[replace ? 'replace' : 'push'](as || href, {\n                scroll: routerScroll\n            });\n        }\n    };\n    navigate();\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === 'string') {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\n/**\n * A React component that extends the HTML `<a>` element to provide [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation between routes.\n *\n * It is the primary way to navigate between routes in Next.js.\n *\n * Read more: [Next.js docs: `<Link>`](https://nextjs.org/docs/app/api-reference/components/link)\n */ const Link = /*#__PURE__*/ _s(_react.default.forwardRef(_c = _s(function LinkComponent(props, forwardedRef) {\n    _s();\n    let children;\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, locale, onClick, onNavigate, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === 'string' || typeof children === 'number')) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const router = _react.default.useContext(_routercontextsharedruntime.RouterContext);\n    const prefetchEnabled = prefetchProp !== false;\n    if (true) {\n        function createPropError(args) {\n            return Object.defineProperty(new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                value: \"E319\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === 'href') {\n                if (props[key] == null || typeof props[key] !== 'string' && typeof props[key] !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: props[key] === null ? 'null' : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            locale: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true,\n            onNavigate: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === 'as') {\n                if (props[key] && valType !== 'string' && valType !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'locale') {\n                if (props[key] && valType !== 'string') {\n                    throw createPropError({\n                        key,\n                        expected: '`string`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'onClick' || key === 'onMouseEnter' || key === 'onTouchStart' || key === 'onNavigate') {\n                if (props[key] && valType !== 'function') {\n                    throw createPropError({\n                        key,\n                        expected: '`function`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'replace' || key === 'scroll' || key === 'shallow' || key === 'passHref' || key === 'prefetch' || key === 'legacyBehavior') {\n                if (props[key] != null && valType !== 'boolean') {\n                    throw createPropError({\n                        key,\n                        expected: '`boolean`',\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n    }\n    const { href, as } = _react.default.useMemo({\n        \"Link.LinkComponent.useMemo\": ()=>{\n            if (!router) {\n                const resolvedHref = formatStringOrUrl(hrefProp);\n                return {\n                    href: resolvedHref,\n                    as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n                };\n            }\n            const [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(router, hrefProp, true);\n            return {\n                href: resolvedHref,\n                as: asProp ? (0, _resolvehref.resolveHref)(router, asProp) : resolvedAs || resolvedHref\n            };\n        }\n    }[\"Link.LinkComponent.useMemo\"], [\n        router,\n        hrefProp,\n        asProp\n    ]);\n    const previousHref = _react.default.useRef(href);\n    const previousAs = _react.default.useRef(as);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw Object.defineProperty(new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E320\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                throw Object.defineProperty(new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                    value: \"E266\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === 'a') {\n                throw Object.defineProperty(new Error('Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E209\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === 'object' && child.ref : forwardedRef;\n    const [setIntersectionRef, isVisible, resetVisible] = (0, _useintersection.useIntersection)({\n        rootMargin: '200px'\n    });\n    const setIntersectionWithResetRef = _react.default.useCallback({\n        \"Link.LinkComponent.useCallback[setIntersectionWithResetRef]\": (el)=>{\n            // Before the link getting observed, check if visible state need to be reset\n            if (previousAs.current !== as || previousHref.current !== href) {\n                resetVisible();\n                previousAs.current = as;\n                previousHref.current = href;\n            }\n            setIntersectionRef(el);\n        }\n    }[\"Link.LinkComponent.useCallback[setIntersectionWithResetRef]\"], [\n        as,\n        href,\n        resetVisible,\n        setIntersectionRef\n    ]);\n    const setRef = (0, _usemergedref.useMergedRef)(setIntersectionWithResetRef, childRef);\n    // Prefetch the URL if we haven't already and it's visible.\n    _react.default.useEffect({\n        \"Link.LinkComponent.useEffect\": ()=>{\n            // in dev, we only prefetch on hover to avoid wasting resources as the prefetch will trigger compiling the page.\n            if (true) {\n                return;\n            }\n            if (!router) {\n                return;\n            }\n            // If we don't need to prefetch the URL, don't do prefetch.\n            if (!isVisible || !prefetchEnabled) {\n                return;\n            }\n            // Prefetch the URL.\n            prefetch(router, href, as, {\n                locale\n            });\n        }\n    }[\"Link.LinkComponent.useEffect\"], [\n        as,\n        href,\n        isVisible,\n        locale,\n        prefetchEnabled,\n        router == null ? void 0 : router.locale,\n        router\n    ]);\n    const childProps = {\n        ref: setRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw Object.defineProperty(new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E312\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n            if (!legacyBehavior && typeof onClick === 'function') {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === 'function') {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, router, href, as, replace, shallow, scroll, locale, onNavigate);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === 'function') {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            });\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === 'function') {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            });\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the domain and locale.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === 'a' && !('href' in child.props)) {\n        const curLocale = typeof locale !== 'undefined' ? locale : router == null ? void 0 : router.locale;\n        // we only render domain locales if we are currently on a domain locale\n        // so that locale links are still visitable in development/preview envs\n        const localeDomain = (router == null ? void 0 : router.isLocaleDomain) && (0, _getdomainlocale.getDomainLocale)(as, curLocale, router == null ? void 0 : router.locales, router == null ? void 0 : router.domainLocales);\n        childProps.href = localeDomain || (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, curLocale, router == null ? void 0 : router.defaultLocale));\n    }\n    if (legacyBehavior) {\n        if (true) {\n            (0, _erroronce.errorOnce)('`legacyBehavior` is deprecated and will be removed in a future ' + 'release. A codemod is available to upgrade your components:\\n\\n' + 'npx @next/codemod@latest new-link .\\n\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components');\n        }\n        return /*#__PURE__*/ _react.default.cloneElement(child, childProps);\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n        ...restProps,\n        ...childProps,\n        children: children\n    });\n}, \"cOEYCKQNQkvb3D7rzCm6zrDrstY=\")), \"cOEYCKQNQkvb3D7rzCm6zrDrstY=\");\n_c1 = Link;\nconst LinkStatusContext = /*#__PURE__*/ (0, _react.createContext)({\n    // We do not support link status in the Pages Router, so we always return false\n    pending: false\n});\nconst useLinkStatus = ()=>{\n    // This behaviour is like React's useFormStatus. When the component is not under\n    // a <form> tag, it will get the default value, instead of throwing an error.\n    return (0, _react.useContext)(LinkStatusContext);\n};\nconst _default = Link;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Link$_react.default.forwardRef\");\n$RefreshReg$(_c1, \"Link\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2xpbmsuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7SUE4ckJBLE9BQW1CO2VBQW5COztJQU5hQSxhQUFhO2VBQWJBOzs7Ozs2RUFqckJvQzt5Q0FFckI7d0NBQ0Q7dUNBQ0Q7bUNBQ0k7dUNBQ0o7d0RBQ0k7NkNBRUU7NkNBQ0E7eUNBQ0o7MENBQ0M7dUNBQ0g7QUEwRzFCLE1BQU1DLGFBQWEsSUFBSUM7QUFVdkIsU0FBU0MsU0FDUEMsTUFBa0IsRUFDbEJDLElBQVksRUFDWkMsRUFBVSxFQUNWQyxPQUF3QjtJQUV4QixJQUFJLEtBQTZCLEVBQUUsRUFFbEM7SUFFRCxJQUFJLENBQUNFLENBQUFBLEdBQUFBLFlBQUFBLFVBQUFBLEVBQVdKLE9BQU87UUFDckI7SUFDRjtJQUVBLDRFQUE0RTtJQUM1RSxZQUFZO0lBQ1osSUFBSSxDQUFDRSxRQUFRRyxxQkFBcUIsRUFBRTtRQUNsQyxNQUFNQyxTQUNKLE9BQ09KLFFBQVFJLE1BQU0sS0FBSyxjQUN0QkosUUFBUUksTUFBTSxHQUVkLFFBSjZELElBSWpEUCxTQUNWQSxPQUFPTyxNQUFNLEdBQ2JDO1FBRVIsTUFBTUMsZ0JBQWdCUixPQUFPLE1BQU1DLEtBQUssTUFBTUs7UUFFOUMsa0VBQWtFO1FBQ2xFLElBQUlWLFdBQVdhLEdBQUcsQ0FBQ0QsZ0JBQWdCO1lBQ2pDO1FBQ0Y7UUFFQSwrQkFBK0I7UUFDL0JaLFdBQVdjLEdBQUcsQ0FBQ0Y7SUFDakI7SUFFQSx1REFBdUQ7SUFDdkQsMERBQTBEO0lBQzFELHNEQUFzRDtJQUN0RCx5REFBeUQ7SUFDekRULE9BQU9ELFFBQVEsQ0FBQ0UsTUFBTUMsSUFBSUMsU0FBU1MsS0FBSyxDQUFDLENBQUNDO1FBQ3hDLElBMEo4RDBELElBMUp0QyxFQUFtQjtZQUN6QyxxQ0FBcUM7WUFDckMsTUFBTTFEO1FBQ1I7SUFDRjtBQUNGO0FBRUEsU0FBU0ksZ0JBQWdCQyxLQUF1QjtJQUM5QyxNQUFNQyxjQUFjRCxNQUFNRSxhQUFhO0lBQ3ZDLE1BQU1DLFNBQVNGLFlBQVlHLFlBQVksQ0FBQztJQUN4QyxPQUNHRCxVQUFVQSxXQUFXLFdBQ3RCSCxNQUFNSyxPQUFPLElBQ2JMLE1BQU1NLE9BQU8sSUFDYk4sTUFBTU8sUUFBUSxJQUNkUCxNQUFNUSxNQUFNLElBQUksNkJBQTZCO0lBQzVDUixNQUFNUyxXQUFXLElBQUlULE1BQU1TLFdBQVcsQ0FBQ0MsS0FBSyxLQUFLO0FBRXREO0FBRUEsU0FBU0MsWUFDUEMsQ0FBbUIsRUFDbkI5QixNQUFzQyxFQUN0Q0MsSUFBWSxFQUNaQyxFQUFVLEVBQ1Y2QixPQUFpQixFQUNqQkMsT0FBaUIsRUFDakJDLE1BQWdCLEVBQ2hCMUIsTUFBdUIsRUFDdkIyQixVQUFtQztJQUVuQyxNQUFNLEVBQUVDLFFBQVEsRUFBRSxHQUFHTCxFQUFFVixhQUFhO0lBRXBDLGtEQUFrRDtJQUNsRCxNQUFNZ0IsbUJBQW1CRCxTQUFTRSxXQUFXLE9BQU87SUFFcEQsSUFDR0Qsb0JBQW9CbkIsZ0JBQWdCYSxNQUNyQ0EsRUFBRVYsYUFBYSxDQUFDa0IsWUFBWSxDQUFDLGFBQzdCO1FBQ0EsOENBQThDO1FBQzlDO0lBQ0Y7SUFFQSxJQUFJLENBQUNqQyxDQUFBQSxHQUFBQSxZQUFBQSxVQUFBQSxFQUFXSixPQUFPO1FBQ3JCLElBQUk4QixTQUFTO1lBQ1gsOERBQThEO1lBQzlELCtCQUErQjtZQUMvQkQsRUFBRVMsY0FBYztZQUNoQkMsU0FBU1QsT0FBTyxDQUFDOUI7UUFDbkI7UUFFQSw4Q0FBOEM7UUFDOUM7SUFDRjtJQUVBNkIsRUFBRVMsY0FBYztJQUVoQixNQUFNRSxXQUFXO1FBQ2YsSUFBSVAsWUFBWTtZQUNkLElBQUlRLHFCQUFxQjtZQUV6QlIsV0FBVztnQkFDVEssZ0JBQWdCO29CQUNkRyxxQkFBcUI7Z0JBQ3ZCO1lBQ0Y7WUFFQSxJQUFJQSxvQkFBb0I7Z0JBQ3RCO1lBQ0Y7UUFDRjtRQUVBLHdFQUF3RTtRQUN4RSxNQUFNQyxlQUFlVixVQUFBQSxPQUFBQSxTQUFVO1FBQy9CLElBQUksb0JBQW9CakMsUUFBUTtZQUM5QkEsTUFBTSxDQUFDK0IsVUFBVSxZQUFZLE9BQU8sQ0FBQzlCLE1BQU1DLElBQUk7Z0JBQzdDOEI7Z0JBQ0F6QjtnQkFDQTBCLFFBQVFVO1lBQ1Y7UUFDRixPQUFPO1lBQ0wzQyxNQUFNLENBQUMrQixVQUFVLFlBQVksT0FBTyxDQUFDN0IsTUFBTUQsTUFBTTtnQkFDL0NnQyxRQUFRVTtZQUNWO1FBQ0Y7SUFDRjtJQUVBRjtBQUNGO0FBT0EsU0FBU0csa0JBQWtCQyxjQUFrQztJQUMzRCxJQUFJLE9BQU9BLG1CQUFtQixVQUFVO1FBQ3RDLE9BQU9BO0lBQ1Q7SUFFQSxPQUFPQyxDQUFBQSxHQUFBQSxXQUFBQSxTQUFBQSxFQUFVRDtBQUNuQjtBQUVBOzs7Ozs7O0NBT0MsR0FDRCxNQUFNRSxPQUFBQSxXQUFBQSxNQUFPQyxPQUFBQSxPQUFLLENBQUNDLFVBQVUsU0FDM0IsU0FBU0MsY0FBY0MsS0FBSyxFQUFFQyxZQUFZOztJQUN4QyxJQUFJQztJQUVKLE1BQU0sRUFDSnBELE1BQU1xRCxRQUFRLEVBQ2RwRCxJQUFJcUQsTUFBTSxFQUNWRixVQUFVRyxZQUFZLEVBQ3RCekQsVUFBVTBELGVBQWUsSUFBSSxFQUM3QkMsUUFBUSxFQUNSM0IsT0FBTyxFQUNQQyxPQUFPLEVBQ1BDLE1BQU0sRUFDTjFCLE1BQU0sRUFDTm9ELE9BQU8sRUFDUHpCLFVBQVUsRUFDVjBCLGNBQWNDLGdCQUFnQixFQUM5QkMsY0FBY0MsZ0JBQWdCLEVBQzlCQyxpQkFBaUIsS0FBSyxFQUN0QixHQUFHQyxXQUNKLEdBQUdkO0lBRUpFLFdBQVdHO0lBRVgsSUFDRVEsa0JBQ0MsUUFBT1gsYUFBYSxZQUFZLE9BQU9BLGFBQWEsU0FBTyxFQUM1RDtRQUNBQSxXQUFBQSxXQUFBQSxHQUFXLHFCQUFDYSxLQUFBQTtzQkFBR2I7O0lBQ2pCO0lBRUEsTUFBTXJELFNBQVNnRCxPQUFBQSxPQUFLLENBQUNtQixVQUFVLENBQUNDLDRCQUFBQSxhQUFhO0lBRTdDLE1BQU1DLGtCQUFrQlosaUJBQWlCO0lBRXpDLElBQUkzQyxJQUFvQixFQUFtQjtRQUN6QyxTQUFTd0QsZ0JBQWdCQyxJQUl4QjtZQUNDLE9BQU8scUJBTU4sQ0FOTSxJQUFJQyxNQUNSLGlDQUErQkQsS0FBS0UsR0FBRyxHQUFDLGlCQUFlRixLQUFLRyxRQUFRLEdBQUMsNEJBQTRCSCxLQUFLSSxNQUFNLEdBQUMsZUFDM0csTUFBNkIsR0FFMUIscUVBQ0EsRUFBQyxHQUxGO3VCQUFBOzRCQUFBOzhCQUFBO1lBTVA7UUFDRjtRQUVBLHNDQUFzQztRQUN0QyxNQUFNQyxxQkFBc0Q7WUFDMUQzRSxNQUFNO1FBQ1I7UUFDQSxNQUFNNEUsZ0JBQXFDQyxPQUFPQyxJQUFJLENBQ3BESDtRQUVGQyxjQUFjRyxPQUFPLENBQUMsQ0FBQ1A7WUFDckIsSUFBSUEsUUFBUSxRQUFRO2dCQUNsQixJQUNFdEIsS0FBSyxDQUFDc0IsSUFBSSxJQUFJLFFBQ2IsT0FBT3RCLEtBQUssQ0FBQ3NCLElBQUksS0FBSyxZQUFZLE9BQU90QixLQUFLLENBQUNzQixJQUFJLEtBQUssVUFDekQ7b0JBQ0EsTUFBTUgsZ0JBQWdCO3dCQUNwQkc7d0JBQ0FDLFVBQVU7d0JBQ1ZDLFFBQVF4QixLQUFLLENBQUNzQixJQUFJLEtBQUssT0FBTyxTQUFTLE9BQU90QixLQUFLLENBQUNzQixJQUFJO29CQUMxRDtnQkFDRjtZQUNGLE9BQU87Z0JBQ0wsc0NBQXNDO2dCQUN0Qyw2REFBNkQ7Z0JBQzdELE1BQU1RLElBQVdSO1lBQ25CO1FBQ0Y7UUFFQSxzQ0FBc0M7UUFDdEMsTUFBTVMscUJBQXNEO1lBQzFEaEYsSUFBSTtZQUNKNkIsU0FBUztZQUNURSxRQUFRO1lBQ1JELFNBQVM7WUFDVDBCLFVBQVU7WUFDVjNELFVBQVU7WUFDVlEsUUFBUTtZQUNSb0QsU0FBUztZQUNUQyxjQUFjO1lBQ2RFLGNBQWM7WUFDZEUsZ0JBQWdCO1lBQ2hCOUIsWUFBWTtRQUNkO1FBQ0EsTUFBTWlELGdCQUFxQ0wsT0FBT0MsSUFBSSxDQUNwREc7UUFFRkMsY0FBY0gsT0FBTyxDQUFDLENBQUNQO1lBQ3JCLE1BQU1XLFVBQVUsT0FBT2pDLEtBQUssQ0FBQ3NCLElBQUk7WUFFakMsSUFBSUEsUUFBUSxNQUFNO2dCQUNoQixJQUFJdEIsS0FBSyxDQUFDc0IsSUFBSSxJQUFJVyxZQUFZLFlBQVlBLFlBQVksVUFBVTtvQkFDOUQsTUFBTWQsZ0JBQWdCO3dCQUNwQkc7d0JBQ0FDLFVBQVU7d0JBQ1ZDLFFBQVFTO29CQUNWO2dCQUNGO1lBQ0YsT0FBTyxJQUFJWCxRQUFRLFVBQVU7Z0JBQzNCLElBQUl0QixLQUFLLENBQUNzQixJQUFJLElBQUlXLFlBQVksVUFBVTtvQkFDdEMsTUFBTWQsZ0JBQWdCO3dCQUNwQkc7d0JBQ0FDLFVBQVU7d0JBQ1ZDLFFBQVFTO29CQUNWO2dCQUNGO1lBQ0YsT0FBTyxJQUNMWCxRQUFRLGFBQ1JBLFFBQVEsa0JBQ1JBLFFBQVEsa0JBQ1JBLFFBQVEsY0FDUjtnQkFDQSxJQUFJdEIsS0FBSyxDQUFDc0IsSUFBSSxJQUFJVyxZQUFZLFlBQVk7b0JBQ3hDLE1BQU1kLGdCQUFnQjt3QkFDcEJHO3dCQUNBQyxVQUFVO3dCQUNWQyxRQUFRUztvQkFDVjtnQkFDRjtZQUNGLE9BQU8sSUFDTFgsUUFBUSxhQUNSQSxRQUFRLFlBQ1JBLFFBQVEsYUFDUkEsUUFBUSxjQUNSQSxRQUFRLGNBQ1JBLFFBQVEsa0JBQ1I7Z0JBQ0EsSUFBSXRCLEtBQUssQ0FBQ3NCLElBQUksSUFBSSxRQUFRVyxZQUFZLFdBQVc7b0JBQy9DLE1BQU1kLGdCQUFnQjt3QkFDcEJHO3dCQUNBQyxVQUFVO3dCQUNWQyxRQUFRUztvQkFDVjtnQkFDRjtZQUNGLE9BQU87Z0JBQ0wsc0NBQXNDO2dCQUN0Qyw2REFBNkQ7Z0JBQzdELE1BQU1ILElBQVdSO1lBQ25CO1FBQ0Y7SUFDRjtJQUVBLE1BQU0sRUFBRXhFLElBQUksRUFBRUMsRUFBRSxFQUFFLEdBQUc4QyxPQUFBQSxPQUFLLENBQUNxQyxPQUFPO3NDQUFDO1lBQ2pDLElBQUksQ0FBQ3JGLFFBQVE7Z0JBQ1gsTUFBTXNGLGVBQWUxQyxrQkFBa0JVO2dCQUN2QyxPQUFPO29CQUNMckQsTUFBTXFGO29CQUNOcEYsSUFBSXFELFNBQVNYLGtCQUFrQlcsVUFBVStCO2dCQUMzQztZQUNGO1lBRUEsTUFBTSxDQUFDQSxjQUFjQyxXQUFXLEdBQUdDLENBQUFBLEdBQUFBLGFBQUFBLFdBQUFBLEVBQVl4RixRQUFRc0QsVUFBVTtZQUVqRSxPQUFPO2dCQUNMckQsTUFBTXFGO2dCQUNOcEYsSUFBSXFELFNBQVNpQyxDQUFBQSxHQUFBQSxhQUFBQSxXQUFBQSxFQUFZeEYsUUFBUXVELFVBQVVnQyxjQUFjRDtZQUMzRDtRQUNGO3FDQUFHO1FBQUN0RjtRQUFRc0Q7UUFBVUM7S0FBTztJQUU3QixNQUFNa0MsZUFBZXpDLE9BQUFBLE9BQUssQ0FBQzBDLE1BQU0sQ0FBU3pGO0lBQzFDLE1BQU0wRixhQUFhM0MsT0FBQUEsT0FBSyxDQUFDMEMsTUFBTSxDQUFTeEY7SUFFeEMsb0ZBQW9GO0lBQ3BGLElBQUkwRjtJQUNKLElBQUk1QixnQkFBZ0I7UUFDbEIsSUFBSWxELElBQW9CLEVBQW9CO1lBQzFDLElBQUk2QyxTQUFTO2dCQUNYa0MsUUFBUUMsSUFBSSxDQUNULG9EQUFvRHhDLFdBQVM7WUFFbEU7WUFDQSxJQUFJTyxrQkFBa0I7Z0JBQ3BCZ0MsUUFBUUMsSUFBSSxDQUNULHlEQUF5RHhDLFdBQVM7WUFFdkU7WUFDQSxJQUFJO2dCQUNGc0MsUUFBUTVDLE9BQUFBLE9BQUssQ0FBQytDLFFBQVEsQ0FBQ0MsSUFBSSxDQUFDM0M7WUFDOUIsRUFBRSxPQUFPeEMsS0FBSztnQkFDWixJQUFJLENBQUN3QyxVQUFVO29CQUNiLE1BQU0scUJBRUwsQ0FGSyxJQUFJbUIsTUFDUCx1REFBdURsQixXQUFTLGtGQUQ3RDsrQkFBQTtvQ0FBQTtzQ0FBQTtvQkFFTjtnQkFDRjtnQkFDQSxNQUFNLHFCQUtMLENBTEssSUFBSWtCLE1BQ1AsNkRBQTZEbEIsV0FBUyw4RkFDcEUsTUFBNkIsR0FDMUIsc0VBQ0EsRUFBQyxHQUpIOzJCQUFBO2dDQUFBO2tDQUFBO2dCQUtOO1lBQ0Y7UUFDRixPQUFPLEVBRU47SUFDSCxPQUFPO1FBQ0wsSUFBSXhDLElBQW9CLEVBQW9CO1lBQzFDLElBQUksQ0FBQ3VDLFlBQUFBLE9BQUFBLEtBQUFBLElBQUFBLFNBQWtCNEMsSUFBQUEsTUFBUyxLQUFLO2dCQUNuQyxNQUFNLHFCQUVMLENBRkssSUFBSXpCLE1BQ1Isb0tBREk7MkJBQUE7Z0NBQUE7a0NBQUE7Z0JBRU47WUFDRjtRQUNGO0lBQ0Y7SUFFQSxNQUFNMEIsV0FBZ0JsQyxpQkFDbEI0QixTQUFTLE9BQU9BLFVBQVUsWUFBWUEsTUFBTU8sR0FBRyxHQUMvQy9DO0lBRUosTUFBTSxDQUFDZ0Qsb0JBQW9CQyxXQUFXQyxhQUFhLEdBQUdDLENBQUFBLEdBQUFBLGlCQUFBQSxlQUFBQSxFQUFnQjtRQUNwRUMsWUFBWTtJQUNkO0lBRUEsTUFBTUMsOEJBQThCekQsT0FBQUEsT0FBSyxDQUFDMEQsV0FBVzt1RUFDbkQsQ0FBQ0M7WUFDQyw0RUFBNEU7WUFDNUUsSUFBSWhCLFdBQVdpQixPQUFPLEtBQUsxRyxNQUFNdUYsYUFBYW1CLE9BQU8sS0FBSzNHLE1BQU07Z0JBQzlEcUc7Z0JBQ0FYLFdBQVdpQixPQUFPLEdBQUcxRztnQkFDckJ1RixhQUFhbUIsT0FBTyxHQUFHM0c7WUFDekI7WUFFQW1HLG1CQUFtQk87UUFDckI7c0VBQ0E7UUFBQ3pHO1FBQUlEO1FBQU1xRztRQUFjRjtLQUFtQjtJQUc5QyxNQUFNUyxTQUFTQyxDQUFBQSxHQUFBQSxjQUFBQSxZQUFBQSxFQUFhTCw2QkFBNkJQO0lBRXpELDJEQUEyRDtJQUMzRGxELE9BQUFBLE9BQUssQ0FBQytELFNBQVM7d0NBQUM7WUFDZCxnSEFBZ0g7WUFDaEgsSUFBSWpHLElBQW9CLEVBQW1CO2dCQUN6QztZQUNGO1lBRUEsSUFBSSxDQUFDZCxRQUFRO2dCQUNYO1lBQ0Y7WUFFQSwyREFBMkQ7WUFDM0QsSUFBSSxDQUFDcUcsYUFBYSxDQUFDaEMsaUJBQWlCO2dCQUNsQztZQUNGO1lBRUEsb0JBQW9CO1lBQ3BCdEUsU0FBU0MsUUFBUUMsTUFBTUMsSUFBSTtnQkFBRUs7WUFBTztRQUN0Qzt1Q0FBRztRQUFDTDtRQUFJRDtRQUFNb0c7UUFBVzlGO1FBQVE4RDtRQUFpQnJFLFVBQUFBLE9BQUFBLEtBQUFBLElBQUFBLE9BQVFPLE1BQU07UUFBRVA7S0FBTztJQUV6RSxNQUFNZ0gsYUFNRjtRQUNGYixLQUFLVTtRQUNMbEQsU0FBUTdCLENBQUM7WUFDUCxJQUFJaEIsSUFBb0IsRUFBbUI7Z0JBQ3pDLElBQUksQ0FBQ2dCLEdBQUc7b0JBQ04sTUFBTSxxQkFFTCxDQUZLLElBQUkwQyxNQUNQLG1GQURHOytCQUFBO29DQUFBO3NDQUFBO29CQUVOO2dCQUNGO1lBQ0Y7WUFFQSxJQUFJLENBQUNSLGtCQUFrQixPQUFPTCxZQUFZLFlBQVk7Z0JBQ3BEQSxRQUFRN0I7WUFDVjtZQUVBLElBQ0VrQyxrQkFDQTRCLE1BQU16QyxLQUFLLElBQ1gsT0FBT3lDLE1BQU16QyxLQUFLLENBQUNRLE9BQU8sS0FBSyxZQUMvQjtnQkFDQWlDLE1BQU16QyxLQUFLLENBQUNRLE9BQU8sQ0FBQzdCO1lBQ3RCO1lBRUEsSUFBSSxDQUFDOUIsUUFBUTtnQkFDWDtZQUNGO1lBRUEsSUFBSThCLEVBQUVtRixnQkFBZ0IsRUFBRTtnQkFDdEI7WUFDRjtZQUVBcEYsWUFDRUMsR0FDQTlCLFFBQ0FDLE1BQ0FDLElBQ0E2QixTQUNBQyxTQUNBQyxRQUNBMUIsUUFDQTJCO1FBRUo7UUFDQTBCLGNBQWE5QixDQUFDO1lBQ1osSUFBSSxDQUFDa0Msa0JBQWtCLE9BQU9ILHFCQUFxQixZQUFZO2dCQUM3REEsaUJBQWlCL0I7WUFDbkI7WUFFQSxJQUNFa0Msa0JBQ0E0QixNQUFNekMsS0FBSyxJQUNYLE9BQU95QyxNQUFNekMsS0FBSyxDQUFDUyxZQUFZLEtBQUssWUFDcEM7Z0JBQ0FnQyxNQUFNekMsS0FBSyxDQUFDUyxZQUFZLENBQUM5QjtZQUMzQjtZQUVBLElBQUksQ0FBQzlCLFFBQVE7Z0JBQ1g7WUFDRjtZQUVBRCxTQUFTQyxRQUFRQyxNQUFNQyxJQUFJO2dCQUN6Qks7Z0JBQ0EyRyxVQUFVO2dCQUNWLGdHQUFnRztnQkFDaEc1Ryx1QkFBdUI7WUFDekI7UUFDRjtRQUNBd0QsY0FBY2hELE1BQXNDLEdBQ2hETixDQUFTQSxHQUNULFNBQVNzRCxhQUFhaEMsQ0FBQztZQUNyQixJQUFJLENBQUNrQyxrQkFBa0IsT0FBT0QscUJBQXFCLFlBQVk7Z0JBQzdEQSxpQkFBaUJqQztZQUNuQjtZQUVBLElBQ0VrQyxrQkFDQTRCLE1BQU16QyxLQUFLLElBQ1gsT0FBT3lDLE1BQU16QyxLQUFLLENBQUNXLFlBQVksS0FBSyxZQUNwQztnQkFDQThCLE1BQU16QyxLQUFLLENBQUNXLFlBQVksQ0FBQ2hDO1lBQzNCO1lBRUEsSUFBSSxDQUFDOUIsUUFBUTtnQkFDWDtZQUNGO1lBRUFELFNBQVNDLFFBQVFDLE1BQU1DLElBQUk7Z0JBQ3pCSztnQkFDQTJHLFVBQVU7Z0JBQ1YsZ0dBQWdHO2dCQUNoRzVHLHVCQUF1QjtZQUN6QjtRQUNGO0lBQ047SUFFQSw2RkFBNkY7SUFDN0Ysd0ZBQXdGO0lBQ3hGLG9GQUFvRjtJQUNwRixJQUFJOEcsQ0FBQUEsR0FBQUEsT0FBQUEsYUFBQUEsRUFBY2xILEtBQUs7UUFDckI4RyxXQUFXL0csSUFBSSxHQUFHQztJQUNwQixPQUFPLElBQ0wsQ0FBQzhELGtCQUNETixZQUNDa0MsTUFBTUssSUFBSSxLQUFLLE9BQU8sQ0FBRSxXQUFVTCxNQUFNekMsS0FBQUEsR0FDekM7UUFDQSxNQUFNa0UsWUFBWSxPQUFPOUcsV0FBVyxjQUFjQSxTQUFTUCxVQUFBQSxPQUFBQSxLQUFBQSxJQUFBQSxPQUFRTyxNQUFNO1FBRXpFLHVFQUF1RTtRQUN2RSx1RUFBdUU7UUFDdkUsTUFBTStHLGVBQ0p0SCxDQUFBQSxVQUFBQSxPQUFBQSxLQUFBQSxJQUFBQSxPQUFRdUgsY0FBQUEsS0FDUkMsQ0FBQUEsR0FBQUEsaUJBQUFBLGVBQUFBLEVBQWdCdEgsSUFBSW1ILFdBQVdySCxVQUFBQSxPQUFBQSxLQUFBQSxJQUFBQSxPQUFReUgsT0FBTyxFQUFFekgsVUFBQUEsT0FBQUEsS0FBQUEsSUFBQUEsT0FBUTBILGFBQWE7UUFFdkVWLFdBQVcvRyxJQUFJLEdBQ2JxSCxnQkFDQUssQ0FBQUEsR0FBQUEsYUFBQUEsV0FBQUEsRUFBWUMsQ0FBQUEsR0FBQUEsV0FBQUEsU0FBQUEsRUFBVTFILElBQUltSCxXQUFXckgsVUFBQUEsT0FBQUEsS0FBQUEsSUFBQUEsT0FBUTZILGFBQWE7SUFDOUQ7SUFFQSxJQUFJN0QsZ0JBQWdCO1FBQ2xCLElBQUlsRCxJQUFvQixFQUFvQjtZQUMxQ2dILENBQUFBLEdBQUFBLFdBQUFBLFNBQUFBLEVBQ0Usb0VBQ0Usb0VBQ0EsNENBQ0E7UUFFTjtRQUNBLHFCQUFPOUUsT0FBQUEsT0FBSyxDQUFDK0UsWUFBWSxDQUFDbkMsT0FBT29CO0lBQ25DO0lBRUEscUJBQ0UscUJBQUM5QyxLQUFBQTtRQUFHLEdBQUdELFNBQVM7UUFBRyxHQUFHK0MsVUFBVTtrQkFDN0IzRDs7QUFHUDs7QUFHRixNQUFNMkUsb0JBQW9CQyxXQUFwQkQsR0FBb0JDLENBQUFBLEdBQUFBLE9BQUFBLGFBQUFBLEVBRXZCO0lBQ0QsK0VBQStFO0lBQy9FQyxTQUFTO0FBQ1g7QUFFTyxNQUFNdEksZ0JBQWdCO0lBQzNCLGdGQUFnRjtJQUNoRiw2RUFBNkU7SUFDN0UsT0FBT3VFLENBQUFBLEdBQUFBLE9BQUFBLFVBQUFBLEVBQVc2RDtBQUNwQjtNQUVBLFdBQWVqRiIsInNvdXJjZXMiOlsiL2hvbWUvdGltby9zcmMvY2xpZW50L2xpbmsudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgdHlwZSB7XG4gIE5leHRSb3V0ZXIsXG4gIFByZWZldGNoT3B0aW9ucyBhcyBSb3V0ZXJQcmVmZXRjaE9wdGlvbnMsXG59IGZyb20gJy4uL3NoYXJlZC9saWIvcm91dGVyL3JvdXRlcidcblxuaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB0eXBlIHsgVXJsT2JqZWN0IH0gZnJvbSAndXJsJ1xuaW1wb3J0IHsgcmVzb2x2ZUhyZWYgfSBmcm9tICcuL3Jlc29sdmUtaHJlZidcbmltcG9ydCB7IGlzTG9jYWxVUkwgfSBmcm9tICcuLi9zaGFyZWQvbGliL3JvdXRlci91dGlscy9pcy1sb2NhbC11cmwnXG5pbXBvcnQgeyBmb3JtYXRVcmwgfSBmcm9tICcuLi9zaGFyZWQvbGliL3JvdXRlci91dGlscy9mb3JtYXQtdXJsJ1xuaW1wb3J0IHsgaXNBYnNvbHV0ZVVybCB9IGZyb20gJy4uL3NoYXJlZC9saWIvdXRpbHMnXG5pbXBvcnQgeyBhZGRMb2NhbGUgfSBmcm9tICcuL2FkZC1sb2NhbGUnXG5pbXBvcnQgeyBSb3V0ZXJDb250ZXh0IH0gZnJvbSAnLi4vc2hhcmVkL2xpYi9yb3V0ZXItY29udGV4dC5zaGFyZWQtcnVudGltZSdcbmltcG9ydCB0eXBlIHsgQXBwUm91dGVySW5zdGFuY2UgfSBmcm9tICcuLi9zaGFyZWQvbGliL2FwcC1yb3V0ZXItY29udGV4dC5zaGFyZWQtcnVudGltZSdcbmltcG9ydCB7IHVzZUludGVyc2VjdGlvbiB9IGZyb20gJy4vdXNlLWludGVyc2VjdGlvbidcbmltcG9ydCB7IGdldERvbWFpbkxvY2FsZSB9IGZyb20gJy4vZ2V0LWRvbWFpbi1sb2NhbGUnXG5pbXBvcnQgeyBhZGRCYXNlUGF0aCB9IGZyb20gJy4vYWRkLWJhc2UtcGF0aCdcbmltcG9ydCB7IHVzZU1lcmdlZFJlZiB9IGZyb20gJy4vdXNlLW1lcmdlZC1yZWYnXG5pbXBvcnQgeyBlcnJvck9uY2UgfSBmcm9tICcuLi9zaGFyZWQvbGliL3V0aWxzL2Vycm9yLW9uY2UnXG5cbnR5cGUgVXJsID0gc3RyaW5nIHwgVXJsT2JqZWN0XG50eXBlIFJlcXVpcmVkS2V5czxUPiA9IHtcbiAgW0sgaW4ga2V5b2YgVF0tPzoge30gZXh0ZW5kcyBQaWNrPFQsIEs+ID8gbmV2ZXIgOiBLXG59W2tleW9mIFRdXG50eXBlIE9wdGlvbmFsS2V5czxUPiA9IHtcbiAgW0sgaW4ga2V5b2YgVF0tPzoge30gZXh0ZW5kcyBQaWNrPFQsIEs+ID8gSyA6IG5ldmVyXG59W2tleW9mIFRdXG5cbnR5cGUgT25OYXZpZ2F0ZUV2ZW50SGFuZGxlciA9IChldmVudDogeyBwcmV2ZW50RGVmYXVsdDogKCkgPT4gdm9pZCB9KSA9PiB2b2lkXG5cbnR5cGUgSW50ZXJuYWxMaW5rUHJvcHMgPSB7XG4gIC8qKlxuICAgKiBUaGUgcGF0aCBvciBVUkwgdG8gbmF2aWdhdGUgdG8uIEl0IGNhbiBhbHNvIGJlIGFuIG9iamVjdC5cbiAgICpcbiAgICogQGV4YW1wbGUgaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBpLXJlZmVyZW5jZS9uZXh0L2xpbmsjd2l0aC11cmwtb2JqZWN0XG4gICAqL1xuICBocmVmOiBVcmxcbiAgLyoqXG4gICAqIE9wdGlvbmFsIGRlY29yYXRvciBmb3IgdGhlIHBhdGggdGhhdCB3aWxsIGJlIHNob3duIGluIHRoZSBicm93c2VyIFVSTCBiYXIuIEJlZm9yZSBOZXh0LmpzIDkuNS4zIHRoaXMgd2FzIHVzZWQgZm9yIGR5bmFtaWMgcm91dGVzLCBjaGVjayBvdXIgW3ByZXZpb3VzIGRvY3NdKGh0dHBzOi8vZ2l0aHViLmNvbS92ZXJjZWwvbmV4dC5qcy9ibG9iL3Y5LjUuMi9kb2NzL2FwaS1yZWZlcmVuY2UvbmV4dC9saW5rLm1kI2R5bmFtaWMtcm91dGVzKSB0byBzZWUgaG93IGl0IHdvcmtlZC4gTm90ZTogd2hlbiB0aGlzIHBhdGggZGlmZmVycyBmcm9tIHRoZSBvbmUgcHJvdmlkZWQgaW4gYGhyZWZgIHRoZSBwcmV2aW91cyBgaHJlZmAvYGFzYCBiZWhhdmlvciBpcyB1c2VkIGFzIHNob3duIGluIHRoZSBbcHJldmlvdXMgZG9jc10oaHR0cHM6Ly9naXRodWIuY29tL3ZlcmNlbC9uZXh0LmpzL2Jsb2IvdjkuNS4yL2RvY3MvYXBpLXJlZmVyZW5jZS9uZXh0L2xpbmsubWQjZHluYW1pYy1yb3V0ZXMpLlxuICAgKi9cbiAgYXM/OiBVcmxcbiAgLyoqXG4gICAqIFJlcGxhY2UgdGhlIGN1cnJlbnQgYGhpc3RvcnlgIHN0YXRlIGluc3RlYWQgb2YgYWRkaW5nIGEgbmV3IHVybCBpbnRvIHRoZSBzdGFjay5cbiAgICpcbiAgICogQGRlZmF1bHRWYWx1ZSBgZmFsc2VgXG4gICAqL1xuICByZXBsYWNlPzogYm9vbGVhblxuICAvKipcbiAgICogV2hldGhlciB0byBvdmVycmlkZSB0aGUgZGVmYXVsdCBzY3JvbGwgYmVoYXZpb3JcbiAgICpcbiAgICogQGV4YW1wbGUgaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBpLXJlZmVyZW5jZS9uZXh0L2xpbmsjZGlzYWJsZS1zY3JvbGxpbmctdG8tdGhlLXRvcC1vZi10aGUtcGFnZVxuICAgKlxuICAgKiBAZGVmYXVsdFZhbHVlIGB0cnVlYFxuICAgKi9cbiAgc2Nyb2xsPzogYm9vbGVhblxuICAvKipcbiAgICogVXBkYXRlIHRoZSBwYXRoIG9mIHRoZSBjdXJyZW50IHBhZ2Ugd2l0aG91dCByZXJ1bm5pbmcgW2BnZXRTdGF0aWNQcm9wc2BdKGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL3BhZ2VzL2J1aWxkaW5nLXlvdXItYXBwbGljYXRpb24vZGF0YS1mZXRjaGluZy9nZXQtc3RhdGljLXByb3BzKSwgW2BnZXRTZXJ2ZXJTaWRlUHJvcHNgXShodHRwczovL25leHRqcy5vcmcvZG9jcy9wYWdlcy9idWlsZGluZy15b3VyLWFwcGxpY2F0aW9uL2RhdGEtZmV0Y2hpbmcvZ2V0LXNlcnZlci1zaWRlLXByb3BzKSBvciBbYGdldEluaXRpYWxQcm9wc2BdKC9kb2NzL3BhZ2VzL2FwaS1yZWZlcmVuY2UvZnVuY3Rpb25zL2dldC1pbml0aWFsLXByb3BzKS5cbiAgICpcbiAgICogQGRlZmF1bHRWYWx1ZSBgZmFsc2VgXG4gICAqL1xuICBzaGFsbG93PzogYm9vbGVhblxuICAvKipcbiAgICogRm9yY2VzIGBMaW5rYCB0byBzZW5kIHRoZSBgaHJlZmAgcHJvcGVydHkgdG8gaXRzIGNoaWxkLlxuICAgKlxuICAgKiBAZGVmYXVsdFZhbHVlIGBmYWxzZWBcbiAgICovXG4gIHBhc3NIcmVmPzogYm9vbGVhblxuICAvKipcbiAgICogUHJlZmV0Y2ggdGhlIHBhZ2UgaW4gdGhlIGJhY2tncm91bmQuXG4gICAqIEFueSBgPExpbmsgLz5gIHRoYXQgaXMgaW4gdGhlIHZpZXdwb3J0IChpbml0aWFsbHkgb3IgdGhyb3VnaCBzY3JvbGwpIHdpbGwgYmUgcHJlZmV0Y2hlZC5cbiAgICogUHJlZmV0Y2ggY2FuIGJlIGRpc2FibGVkIGJ5IHBhc3NpbmcgYHByZWZldGNoPXtmYWxzZX1gLiBQcmVmZXRjaGluZyBpcyBvbmx5IGVuYWJsZWQgaW4gcHJvZHVjdGlvbi5cbiAgICpcbiAgICogSW4gQXBwIFJvdXRlcjpcbiAgICogLSBgbnVsbGAgKGRlZmF1bHQpOiBGb3Igc3RhdGljYWxseSBnZW5lcmF0ZWQgcGFnZXMsIHRoaXMgd2lsbCBwcmVmZXRjaCB0aGUgZnVsbCBSZWFjdCBTZXJ2ZXIgQ29tcG9uZW50IGRhdGEuIEZvciBkeW5hbWljIHBhZ2VzLCB0aGlzIHdpbGwgcHJlZmV0Y2ggdXAgdG8gdGhlIG5lYXJlc3Qgcm91dGUgc2VnbWVudCB3aXRoIGEgW2Bsb2FkaW5nLmpzYF0oaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2FwaS1yZWZlcmVuY2UvZmlsZS1jb252ZW50aW9ucy9sb2FkaW5nKSBmaWxlLiBJZiB0aGVyZSBpcyBubyBsb2FkaW5nIGZpbGUsIGl0IHdpbGwgbm90IGZldGNoIHRoZSBmdWxsIHRyZWUgdG8gYXZvaWQgZmV0Y2hpbmcgdG9vIG11Y2ggZGF0YS5cbiAgICogLSBgdHJ1ZWA6IFRoaXMgd2lsbCBwcmVmZXRjaCB0aGUgZnVsbCBSZWFjdCBTZXJ2ZXIgQ29tcG9uZW50IGRhdGEgZm9yIGFsbCByb3V0ZSBzZWdtZW50cywgcmVnYXJkbGVzcyBvZiB3aGV0aGVyIHRoZXkgY29udGFpbiBhIHNlZ21lbnQgd2l0aCBgbG9hZGluZy5qc2AuXG4gICAqIC0gYGZhbHNlYDogVGhpcyB3aWxsIG5vdCBwcmVmZXRjaCBhbnkgZGF0YSwgZXZlbiBvbiBob3Zlci5cbiAgICpcbiAgICogSW4gUGFnZXMgUm91dGVyOlxuICAgKiAtIGB0cnVlYCAoZGVmYXVsdCk6IFRoZSBmdWxsIHJvdXRlICYgaXRzIGRhdGEgd2lsbCBiZSBwcmVmZXRjaGVkLlxuICAgKiAtIGBmYWxzZWA6IFByZWZldGNoaW5nIHdpbGwgbm90IGhhcHBlbiB3aGVuIGVudGVyaW5nIHRoZSB2aWV3cG9ydCwgYnV0IHdpbGwgc3RpbGwgaGFwcGVuIG9uIGhvdmVyLlxuICAgKiBAZGVmYXVsdFZhbHVlIGB0cnVlYCAocGFnZXMgcm91dGVyKSBvciBgbnVsbGAgKGFwcCByb3V0ZXIpXG4gICAqL1xuICBwcmVmZXRjaD86IGJvb2xlYW4gfCBudWxsXG4gIC8qKlxuICAgKiBUaGUgYWN0aXZlIGxvY2FsZSBpcyBhdXRvbWF0aWNhbGx5IHByZXBlbmRlZC4gYGxvY2FsZWAgYWxsb3dzIGZvciBwcm92aWRpbmcgYSBkaWZmZXJlbnQgbG9jYWxlLlxuICAgKiBXaGVuIGBmYWxzZWAgYGhyZWZgIGhhcyB0byBpbmNsdWRlIHRoZSBsb2NhbGUgYXMgdGhlIGRlZmF1bHQgYmVoYXZpb3IgaXMgZGlzYWJsZWQuXG4gICAqIE5vdGU6IFRoaXMgaXMgb25seSBhdmFpbGFibGUgaW4gdGhlIFBhZ2VzIFJvdXRlci5cbiAgICovXG4gIGxvY2FsZT86IHN0cmluZyB8IGZhbHNlXG4gIC8qKlxuICAgKiBFbmFibGUgbGVnYWN5IGxpbmsgYmVoYXZpb3IuXG4gICAqIEBkZXByZWNhdGVkIFRoaXMgd2lsbCBiZSByZW1vdmVkIGluIHYxNlxuICAgKiBAZGVmYXVsdFZhbHVlIGBmYWxzZWBcbiAgICogQHNlZSBodHRwczovL2dpdGh1Yi5jb20vdmVyY2VsL25leHQuanMvY29tbWl0LzQ4OWU2NWVkOTg1NDRlNjliMGFmZDdlMGNmYzNmOWY2YzJiODAzYjdcbiAgICovXG4gIGxlZ2FjeUJlaGF2aW9yPzogYm9vbGVhblxuICAvKipcbiAgICogT3B0aW9uYWwgZXZlbnQgaGFuZGxlciBmb3Igd2hlbiB0aGUgbW91c2UgcG9pbnRlciBpcyBtb3ZlZCBvbnRvIExpbmtcbiAgICovXG4gIG9uTW91c2VFbnRlcj86IFJlYWN0Lk1vdXNlRXZlbnRIYW5kbGVyPEhUTUxBbmNob3JFbGVtZW50PlxuICAvKipcbiAgICogT3B0aW9uYWwgZXZlbnQgaGFuZGxlciBmb3Igd2hlbiBMaW5rIGlzIHRvdWNoZWQuXG4gICAqL1xuICBvblRvdWNoU3RhcnQ/OiBSZWFjdC5Ub3VjaEV2ZW50SGFuZGxlcjxIVE1MQW5jaG9yRWxlbWVudD5cbiAgLyoqXG4gICAqIE9wdGlvbmFsIGV2ZW50IGhhbmRsZXIgZm9yIHdoZW4gTGluayBpcyBjbGlja2VkLlxuICAgKi9cbiAgb25DbGljaz86IFJlYWN0Lk1vdXNlRXZlbnRIYW5kbGVyPEhUTUxBbmNob3JFbGVtZW50PlxuICAvKipcbiAgICogT3B0aW9uYWwgZXZlbnQgaGFuZGxlciBmb3Igd2hlbiB0aGUgYDxMaW5rPmAgaXMgbmF2aWdhdGVkLlxuICAgKi9cbiAgb25OYXZpZ2F0ZT86IE9uTmF2aWdhdGVFdmVudEhhbmRsZXJcbn1cblxuLy8gVE9ETy1BUFA6IEluY2x1ZGUgdGhlIGZ1bGwgc2V0IG9mIEFuY2hvciBwcm9wc1xuLy8gYWRkaW5nIHRoaXMgdG8gdGhlIHB1YmxpY2x5IGV4cG9ydGVkIHR5cGUgY3VycmVudGx5IGJyZWFrcyBleGlzdGluZyBhcHBzXG5cbi8vIGBSb3V0ZUluZmVyVHlwZWAgaXMgYSBzdHViIGhlcmUgdG8gYXZvaWQgYnJlYWtpbmcgYHR5cGVkUm91dGVzYCB3aGVuIHRoZSB0eXBlXG4vLyBpc24ndCBnZW5lcmF0ZWQgeWV0LiBJdCB3aWxsIGJlIHJlcGxhY2VkIHdoZW4gdGhlIHdlYnBhY2sgcGx1Z2luIHJ1bnMuXG4vLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLXVudXNlZC12YXJzXG5leHBvcnQgdHlwZSBMaW5rUHJvcHM8Um91dGVJbmZlclR5cGUgPSBhbnk+ID0gSW50ZXJuYWxMaW5rUHJvcHNcbnR5cGUgTGlua1Byb3BzUmVxdWlyZWQgPSBSZXF1aXJlZEtleXM8TGlua1Byb3BzPlxudHlwZSBMaW5rUHJvcHNPcHRpb25hbCA9IE9wdGlvbmFsS2V5czxJbnRlcm5hbExpbmtQcm9wcz5cblxuY29uc3QgcHJlZmV0Y2hlZCA9IG5ldyBTZXQ8c3RyaW5nPigpXG5cbnR5cGUgUHJlZmV0Y2hPcHRpb25zID0gUm91dGVyUHJlZmV0Y2hPcHRpb25zICYge1xuICAvKipcbiAgICogYnlwYXNzUHJlZmV0Y2hlZENoZWNrIHdpbGwgYnlwYXNzIHRoZSBjaGVjayB0byBzZWUgaWYgdGhlIGBocmVmYCBoYXNcbiAgICogYWxyZWFkeSBiZWVuIGZldGNoZWQuXG4gICAqL1xuICBieXBhc3NQcmVmZXRjaGVkQ2hlY2s/OiBib29sZWFuXG59XG5cbmZ1bmN0aW9uIHByZWZldGNoKFxuICByb3V0ZXI6IE5leHRSb3V0ZXIsXG4gIGhyZWY6IHN0cmluZyxcbiAgYXM6IHN0cmluZyxcbiAgb3B0aW9uczogUHJlZmV0Y2hPcHRpb25zXG4pOiB2b2lkIHtcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgcmV0dXJuXG4gIH1cblxuICBpZiAoIWlzTG9jYWxVUkwoaHJlZikpIHtcbiAgICByZXR1cm5cbiAgfVxuXG4gIC8vIFdlIHNob3VsZCBvbmx5IGRlZHVwZSByZXF1ZXN0cyB3aGVuIGV4cGVyaW1lbnRhbC5vcHRpbWlzdGljQ2xpZW50Q2FjaGUgaXNcbiAgLy8gZGlzYWJsZWQuXG4gIGlmICghb3B0aW9ucy5ieXBhc3NQcmVmZXRjaGVkQ2hlY2spIHtcbiAgICBjb25zdCBsb2NhbGUgPVxuICAgICAgLy8gTGV0IHRoZSBsaW5rJ3MgbG9jYWxlIHByb3Agb3ZlcnJpZGUgdGhlIGRlZmF1bHQgcm91dGVyIGxvY2FsZS5cbiAgICAgIHR5cGVvZiBvcHRpb25zLmxvY2FsZSAhPT0gJ3VuZGVmaW5lZCdcbiAgICAgICAgPyBvcHRpb25zLmxvY2FsZVxuICAgICAgICA6IC8vIE90aGVyd2lzZSBmYWxsYmFjayB0byB0aGUgcm91dGVyJ3MgbG9jYWxlLlxuICAgICAgICAgICdsb2NhbGUnIGluIHJvdXRlclxuICAgICAgICAgID8gcm91dGVyLmxvY2FsZVxuICAgICAgICAgIDogdW5kZWZpbmVkXG5cbiAgICBjb25zdCBwcmVmZXRjaGVkS2V5ID0gaHJlZiArICclJyArIGFzICsgJyUnICsgbG9jYWxlXG5cbiAgICAvLyBJZiB3ZSd2ZSBhbHJlYWR5IGZldGNoZWQgdGhlIGtleSwgdGhlbiBkb24ndCBwcmVmZXRjaCBpdCBhZ2FpbiFcbiAgICBpZiAocHJlZmV0Y2hlZC5oYXMocHJlZmV0Y2hlZEtleSkpIHtcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIC8vIE1hcmsgdGhpcyBVUkwgYXMgcHJlZmV0Y2hlZC5cbiAgICBwcmVmZXRjaGVkLmFkZChwcmVmZXRjaGVkS2V5KVxuICB9XG5cbiAgLy8gUHJlZmV0Y2ggdGhlIEpTT04gcGFnZSBpZiBhc2tlZCAob25seSBpbiB0aGUgY2xpZW50KVxuICAvLyBXZSBuZWVkIHRvIGhhbmRsZSBhIHByZWZldGNoIGVycm9yIGhlcmUgc2luY2Ugd2UgbWF5IGJlXG4gIC8vIGxvYWRpbmcgd2l0aCBwcmlvcml0eSB3aGljaCBjYW4gcmVqZWN0IGJ1dCB3ZSBkb24ndFxuICAvLyB3YW50IHRvIGZvcmNlIG5hdmlnYXRpb24gc2luY2UgdGhpcyBpcyBvbmx5IGEgcHJlZmV0Y2hcbiAgcm91dGVyLnByZWZldGNoKGhyZWYsIGFzLCBvcHRpb25zKS5jYXRjaCgoZXJyKSA9PiB7XG4gICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgICAgIC8vIHJldGhyb3cgdG8gc2hvdyBpbnZhbGlkIFVSTCBlcnJvcnNcbiAgICAgIHRocm93IGVyclxuICAgIH1cbiAgfSlcbn1cblxuZnVuY3Rpb24gaXNNb2RpZmllZEV2ZW50KGV2ZW50OiBSZWFjdC5Nb3VzZUV2ZW50KTogYm9vbGVhbiB7XG4gIGNvbnN0IGV2ZW50VGFyZ2V0ID0gZXZlbnQuY3VycmVudFRhcmdldCBhcyBIVE1MQW5jaG9yRWxlbWVudCB8IFNWR0FFbGVtZW50XG4gIGNvbnN0IHRhcmdldCA9IGV2ZW50VGFyZ2V0LmdldEF0dHJpYnV0ZSgndGFyZ2V0JylcbiAgcmV0dXJuIChcbiAgICAodGFyZ2V0ICYmIHRhcmdldCAhPT0gJ19zZWxmJykgfHxcbiAgICBldmVudC5tZXRhS2V5IHx8XG4gICAgZXZlbnQuY3RybEtleSB8fFxuICAgIGV2ZW50LnNoaWZ0S2V5IHx8XG4gICAgZXZlbnQuYWx0S2V5IHx8IC8vIHRyaWdnZXJzIHJlc291cmNlIGRvd25sb2FkXG4gICAgKGV2ZW50Lm5hdGl2ZUV2ZW50ICYmIGV2ZW50Lm5hdGl2ZUV2ZW50LndoaWNoID09PSAyKVxuICApXG59XG5cbmZ1bmN0aW9uIGxpbmtDbGlja2VkKFxuICBlOiBSZWFjdC5Nb3VzZUV2ZW50LFxuICByb3V0ZXI6IE5leHRSb3V0ZXIgfCBBcHBSb3V0ZXJJbnN0YW5jZSxcbiAgaHJlZjogc3RyaW5nLFxuICBhczogc3RyaW5nLFxuICByZXBsYWNlPzogYm9vbGVhbixcbiAgc2hhbGxvdz86IGJvb2xlYW4sXG4gIHNjcm9sbD86IGJvb2xlYW4sXG4gIGxvY2FsZT86IHN0cmluZyB8IGZhbHNlLFxuICBvbk5hdmlnYXRlPzogT25OYXZpZ2F0ZUV2ZW50SGFuZGxlclxuKTogdm9pZCB7XG4gIGNvbnN0IHsgbm9kZU5hbWUgfSA9IGUuY3VycmVudFRhcmdldFxuXG4gIC8vIGFuY2hvcnMgaW5zaWRlIGFuIHN2ZyBoYXZlIGEgbG93ZXJjYXNlIG5vZGVOYW1lXG4gIGNvbnN0IGlzQW5jaG9yTm9kZU5hbWUgPSBub2RlTmFtZS50b1VwcGVyQ2FzZSgpID09PSAnQSdcblxuICBpZiAoXG4gICAgKGlzQW5jaG9yTm9kZU5hbWUgJiYgaXNNb2RpZmllZEV2ZW50KGUpKSB8fFxuICAgIGUuY3VycmVudFRhcmdldC5oYXNBdHRyaWJ1dGUoJ2Rvd25sb2FkJylcbiAgKSB7XG4gICAgLy8gaWdub3JlIGNsaWNrIGZvciBicm93c2Vy4oCZcyBkZWZhdWx0IGJlaGF2aW9yXG4gICAgcmV0dXJuXG4gIH1cblxuICBpZiAoIWlzTG9jYWxVUkwoaHJlZikpIHtcbiAgICBpZiAocmVwbGFjZSkge1xuICAgICAgLy8gYnJvd3NlciBkZWZhdWx0IGJlaGF2aW9yIGRvZXMgbm90IHJlcGxhY2UgdGhlIGhpc3Rvcnkgc3RhdGVcbiAgICAgIC8vIHNvIHdlIG5lZWQgdG8gZG8gaXQgbWFudWFsbHlcbiAgICAgIGUucHJldmVudERlZmF1bHQoKVxuICAgICAgbG9jYXRpb24ucmVwbGFjZShocmVmKVxuICAgIH1cblxuICAgIC8vIGlnbm9yZSBjbGljayBmb3IgYnJvd3NlcuKAmXMgZGVmYXVsdCBiZWhhdmlvclxuICAgIHJldHVyblxuICB9XG5cbiAgZS5wcmV2ZW50RGVmYXVsdCgpXG5cbiAgY29uc3QgbmF2aWdhdGUgPSAoKSA9PiB7XG4gICAgaWYgKG9uTmF2aWdhdGUpIHtcbiAgICAgIGxldCBpc0RlZmF1bHRQcmV2ZW50ZWQgPSBmYWxzZVxuXG4gICAgICBvbk5hdmlnYXRlKHtcbiAgICAgICAgcHJldmVudERlZmF1bHQ6ICgpID0+IHtcbiAgICAgICAgICBpc0RlZmF1bHRQcmV2ZW50ZWQgPSB0cnVlXG4gICAgICAgIH0sXG4gICAgICB9KVxuXG4gICAgICBpZiAoaXNEZWZhdWx0UHJldmVudGVkKSB7XG4gICAgICAgIHJldHVyblxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIElmIHRoZSByb3V0ZXIgaXMgYW4gTmV4dFJvdXRlciBpbnN0YW5jZSBpdCB3aWxsIGhhdmUgYGJlZm9yZVBvcFN0YXRlYFxuICAgIGNvbnN0IHJvdXRlclNjcm9sbCA9IHNjcm9sbCA/PyB0cnVlXG4gICAgaWYgKCdiZWZvcmVQb3BTdGF0ZScgaW4gcm91dGVyKSB7XG4gICAgICByb3V0ZXJbcmVwbGFjZSA/ICdyZXBsYWNlJyA6ICdwdXNoJ10oaHJlZiwgYXMsIHtcbiAgICAgICAgc2hhbGxvdyxcbiAgICAgICAgbG9jYWxlLFxuICAgICAgICBzY3JvbGw6IHJvdXRlclNjcm9sbCxcbiAgICAgIH0pXG4gICAgfSBlbHNlIHtcbiAgICAgIHJvdXRlcltyZXBsYWNlID8gJ3JlcGxhY2UnIDogJ3B1c2gnXShhcyB8fCBocmVmLCB7XG4gICAgICAgIHNjcm9sbDogcm91dGVyU2Nyb2xsLFxuICAgICAgfSlcbiAgICB9XG4gIH1cblxuICBuYXZpZ2F0ZSgpXG59XG5cbnR5cGUgTGlua1Byb3BzUmVhbCA9IFJlYWN0LlByb3BzV2l0aENoaWxkcmVuPFxuICBPbWl0PFJlYWN0LkFuY2hvckhUTUxBdHRyaWJ1dGVzPEhUTUxBbmNob3JFbGVtZW50Piwga2V5b2YgTGlua1Byb3BzPiAmXG4gICAgTGlua1Byb3BzXG4+XG5cbmZ1bmN0aW9uIGZvcm1hdFN0cmluZ09yVXJsKHVybE9iak9yU3RyaW5nOiBVcmxPYmplY3QgfCBzdHJpbmcpOiBzdHJpbmcge1xuICBpZiAodHlwZW9mIHVybE9iak9yU3RyaW5nID09PSAnc3RyaW5nJykge1xuICAgIHJldHVybiB1cmxPYmpPclN0cmluZ1xuICB9XG5cbiAgcmV0dXJuIGZvcm1hdFVybCh1cmxPYmpPclN0cmluZylcbn1cblxuLyoqXG4gKiBBIFJlYWN0IGNvbXBvbmVudCB0aGF0IGV4dGVuZHMgdGhlIEhUTUwgYDxhPmAgZWxlbWVudCB0byBwcm92aWRlIFtwcmVmZXRjaGluZ10oaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2J1aWxkaW5nLXlvdXItYXBwbGljYXRpb24vcm91dGluZy9saW5raW5nLWFuZC1uYXZpZ2F0aW5nIzItcHJlZmV0Y2hpbmcpXG4gKiBhbmQgY2xpZW50LXNpZGUgbmF2aWdhdGlvbiBiZXR3ZWVuIHJvdXRlcy5cbiAqXG4gKiBJdCBpcyB0aGUgcHJpbWFyeSB3YXkgdG8gbmF2aWdhdGUgYmV0d2VlbiByb3V0ZXMgaW4gTmV4dC5qcy5cbiAqXG4gKiBSZWFkIG1vcmU6IFtOZXh0LmpzIGRvY3M6IGA8TGluaz5gXShodHRwczovL25leHRqcy5vcmcvZG9jcy9hcHAvYXBpLXJlZmVyZW5jZS9jb21wb25lbnRzL2xpbmspXG4gKi9cbmNvbnN0IExpbmsgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxBbmNob3JFbGVtZW50LCBMaW5rUHJvcHNSZWFsPihcbiAgZnVuY3Rpb24gTGlua0NvbXBvbmVudChwcm9wcywgZm9yd2FyZGVkUmVmKSB7XG4gICAgbGV0IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcblxuICAgIGNvbnN0IHtcbiAgICAgIGhyZWY6IGhyZWZQcm9wLFxuICAgICAgYXM6IGFzUHJvcCxcbiAgICAgIGNoaWxkcmVuOiBjaGlsZHJlblByb3AsXG4gICAgICBwcmVmZXRjaDogcHJlZmV0Y2hQcm9wID0gbnVsbCxcbiAgICAgIHBhc3NIcmVmLFxuICAgICAgcmVwbGFjZSxcbiAgICAgIHNoYWxsb3csXG4gICAgICBzY3JvbGwsXG4gICAgICBsb2NhbGUsXG4gICAgICBvbkNsaWNrLFxuICAgICAgb25OYXZpZ2F0ZSxcbiAgICAgIG9uTW91c2VFbnRlcjogb25Nb3VzZUVudGVyUHJvcCxcbiAgICAgIG9uVG91Y2hTdGFydDogb25Ub3VjaFN0YXJ0UHJvcCxcbiAgICAgIGxlZ2FjeUJlaGF2aW9yID0gZmFsc2UsXG4gICAgICAuLi5yZXN0UHJvcHNcbiAgICB9ID0gcHJvcHNcblxuICAgIGNoaWxkcmVuID0gY2hpbGRyZW5Qcm9wXG5cbiAgICBpZiAoXG4gICAgICBsZWdhY3lCZWhhdmlvciAmJlxuICAgICAgKHR5cGVvZiBjaGlsZHJlbiA9PT0gJ3N0cmluZycgfHwgdHlwZW9mIGNoaWxkcmVuID09PSAnbnVtYmVyJylcbiAgICApIHtcbiAgICAgIGNoaWxkcmVuID0gPGE+e2NoaWxkcmVufTwvYT5cbiAgICB9XG5cbiAgICBjb25zdCByb3V0ZXIgPSBSZWFjdC51c2VDb250ZXh0KFJvdXRlckNvbnRleHQpXG5cbiAgICBjb25zdCBwcmVmZXRjaEVuYWJsZWQgPSBwcmVmZXRjaFByb3AgIT09IGZhbHNlXG5cbiAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICAgICAgZnVuY3Rpb24gY3JlYXRlUHJvcEVycm9yKGFyZ3M6IHtcbiAgICAgICAga2V5OiBzdHJpbmdcbiAgICAgICAgZXhwZWN0ZWQ6IHN0cmluZ1xuICAgICAgICBhY3R1YWw6IHN0cmluZ1xuICAgICAgfSkge1xuICAgICAgICByZXR1cm4gbmV3IEVycm9yKFxuICAgICAgICAgIGBGYWlsZWQgcHJvcCB0eXBlOiBUaGUgcHJvcCBcXGAke2FyZ3Mua2V5fVxcYCBleHBlY3RzIGEgJHthcmdzLmV4cGVjdGVkfSBpbiBcXGA8TGluaz5cXGAsIGJ1dCBnb3QgXFxgJHthcmdzLmFjdHVhbH1cXGAgaW5zdGVhZC5gICtcbiAgICAgICAgICAgICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJ1xuICAgICAgICAgICAgICA/IC8vIFRPRE86IFJlbW92ZSB0aGlzIGFkZGVuZHVtIGlmIE93bmVyIFN0YWNrcyBhcmUgYXZhaWxhYmxlXG4gICAgICAgICAgICAgICAgXCJcXG5PcGVuIHlvdXIgYnJvd3NlcidzIGNvbnNvbGUgdG8gdmlldyB0aGUgQ29tcG9uZW50IHN0YWNrIHRyYWNlLlwiXG4gICAgICAgICAgICAgIDogJycpXG4gICAgICAgIClcbiAgICAgIH1cblxuICAgICAgLy8gVHlwZVNjcmlwdCB0cmljayBmb3IgdHlwZS1ndWFyZGluZzpcbiAgICAgIGNvbnN0IHJlcXVpcmVkUHJvcHNHdWFyZDogUmVjb3JkPExpbmtQcm9wc1JlcXVpcmVkLCB0cnVlPiA9IHtcbiAgICAgICAgaHJlZjogdHJ1ZSxcbiAgICAgIH0gYXMgY29uc3RcbiAgICAgIGNvbnN0IHJlcXVpcmVkUHJvcHM6IExpbmtQcm9wc1JlcXVpcmVkW10gPSBPYmplY3Qua2V5cyhcbiAgICAgICAgcmVxdWlyZWRQcm9wc0d1YXJkXG4gICAgICApIGFzIExpbmtQcm9wc1JlcXVpcmVkW11cbiAgICAgIHJlcXVpcmVkUHJvcHMuZm9yRWFjaCgoa2V5OiBMaW5rUHJvcHNSZXF1aXJlZCkgPT4ge1xuICAgICAgICBpZiAoa2V5ID09PSAnaHJlZicpIHtcbiAgICAgICAgICBpZiAoXG4gICAgICAgICAgICBwcm9wc1trZXldID09IG51bGwgfHxcbiAgICAgICAgICAgICh0eXBlb2YgcHJvcHNba2V5XSAhPT0gJ3N0cmluZycgJiYgdHlwZW9mIHByb3BzW2tleV0gIT09ICdvYmplY3QnKVxuICAgICAgICAgICkge1xuICAgICAgICAgICAgdGhyb3cgY3JlYXRlUHJvcEVycm9yKHtcbiAgICAgICAgICAgICAga2V5LFxuICAgICAgICAgICAgICBleHBlY3RlZDogJ2BzdHJpbmdgIG9yIGBvYmplY3RgJyxcbiAgICAgICAgICAgICAgYWN0dWFsOiBwcm9wc1trZXldID09PSBudWxsID8gJ251bGwnIDogdHlwZW9mIHByb3BzW2tleV0sXG4gICAgICAgICAgICB9KVxuICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAvLyBUeXBlU2NyaXB0IHRyaWNrIGZvciB0eXBlLWd1YXJkaW5nOlxuICAgICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tdW51c2VkLXZhcnNcbiAgICAgICAgICBjb25zdCBfOiBuZXZlciA9IGtleVxuICAgICAgICB9XG4gICAgICB9KVxuXG4gICAgICAvLyBUeXBlU2NyaXB0IHRyaWNrIGZvciB0eXBlLWd1YXJkaW5nOlxuICAgICAgY29uc3Qgb3B0aW9uYWxQcm9wc0d1YXJkOiBSZWNvcmQ8TGlua1Byb3BzT3B0aW9uYWwsIHRydWU+ID0ge1xuICAgICAgICBhczogdHJ1ZSxcbiAgICAgICAgcmVwbGFjZTogdHJ1ZSxcbiAgICAgICAgc2Nyb2xsOiB0cnVlLFxuICAgICAgICBzaGFsbG93OiB0cnVlLFxuICAgICAgICBwYXNzSHJlZjogdHJ1ZSxcbiAgICAgICAgcHJlZmV0Y2g6IHRydWUsXG4gICAgICAgIGxvY2FsZTogdHJ1ZSxcbiAgICAgICAgb25DbGljazogdHJ1ZSxcbiAgICAgICAgb25Nb3VzZUVudGVyOiB0cnVlLFxuICAgICAgICBvblRvdWNoU3RhcnQ6IHRydWUsXG4gICAgICAgIGxlZ2FjeUJlaGF2aW9yOiB0cnVlLFxuICAgICAgICBvbk5hdmlnYXRlOiB0cnVlLFxuICAgICAgfSBhcyBjb25zdFxuICAgICAgY29uc3Qgb3B0aW9uYWxQcm9wczogTGlua1Byb3BzT3B0aW9uYWxbXSA9IE9iamVjdC5rZXlzKFxuICAgICAgICBvcHRpb25hbFByb3BzR3VhcmRcbiAgICAgICkgYXMgTGlua1Byb3BzT3B0aW9uYWxbXVxuICAgICAgb3B0aW9uYWxQcm9wcy5mb3JFYWNoKChrZXk6IExpbmtQcm9wc09wdGlvbmFsKSA9PiB7XG4gICAgICAgIGNvbnN0IHZhbFR5cGUgPSB0eXBlb2YgcHJvcHNba2V5XVxuXG4gICAgICAgIGlmIChrZXkgPT09ICdhcycpIHtcbiAgICAgICAgICBpZiAocHJvcHNba2V5XSAmJiB2YWxUeXBlICE9PSAnc3RyaW5nJyAmJiB2YWxUeXBlICE9PSAnb2JqZWN0Jykge1xuICAgICAgICAgICAgdGhyb3cgY3JlYXRlUHJvcEVycm9yKHtcbiAgICAgICAgICAgICAga2V5LFxuICAgICAgICAgICAgICBleHBlY3RlZDogJ2BzdHJpbmdgIG9yIGBvYmplY3RgJyxcbiAgICAgICAgICAgICAgYWN0dWFsOiB2YWxUeXBlLFxuICAgICAgICAgICAgfSlcbiAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSBpZiAoa2V5ID09PSAnbG9jYWxlJykge1xuICAgICAgICAgIGlmIChwcm9wc1trZXldICYmIHZhbFR5cGUgIT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgICB0aHJvdyBjcmVhdGVQcm9wRXJyb3Ioe1xuICAgICAgICAgICAgICBrZXksXG4gICAgICAgICAgICAgIGV4cGVjdGVkOiAnYHN0cmluZ2AnLFxuICAgICAgICAgICAgICBhY3R1YWw6IHZhbFR5cGUsXG4gICAgICAgICAgICB9KVxuICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIGlmIChcbiAgICAgICAgICBrZXkgPT09ICdvbkNsaWNrJyB8fFxuICAgICAgICAgIGtleSA9PT0gJ29uTW91c2VFbnRlcicgfHxcbiAgICAgICAgICBrZXkgPT09ICdvblRvdWNoU3RhcnQnIHx8XG4gICAgICAgICAga2V5ID09PSAnb25OYXZpZ2F0ZSdcbiAgICAgICAgKSB7XG4gICAgICAgICAgaWYgKHByb3BzW2tleV0gJiYgdmFsVHlwZSAhPT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgdGhyb3cgY3JlYXRlUHJvcEVycm9yKHtcbiAgICAgICAgICAgICAga2V5LFxuICAgICAgICAgICAgICBleHBlY3RlZDogJ2BmdW5jdGlvbmAnLFxuICAgICAgICAgICAgICBhY3R1YWw6IHZhbFR5cGUsXG4gICAgICAgICAgICB9KVxuICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIGlmIChcbiAgICAgICAgICBrZXkgPT09ICdyZXBsYWNlJyB8fFxuICAgICAgICAgIGtleSA9PT0gJ3Njcm9sbCcgfHxcbiAgICAgICAgICBrZXkgPT09ICdzaGFsbG93JyB8fFxuICAgICAgICAgIGtleSA9PT0gJ3Bhc3NIcmVmJyB8fFxuICAgICAgICAgIGtleSA9PT0gJ3ByZWZldGNoJyB8fFxuICAgICAgICAgIGtleSA9PT0gJ2xlZ2FjeUJlaGF2aW9yJ1xuICAgICAgICApIHtcbiAgICAgICAgICBpZiAocHJvcHNba2V5XSAhPSBudWxsICYmIHZhbFR5cGUgIT09ICdib29sZWFuJykge1xuICAgICAgICAgICAgdGhyb3cgY3JlYXRlUHJvcEVycm9yKHtcbiAgICAgICAgICAgICAga2V5LFxuICAgICAgICAgICAgICBleHBlY3RlZDogJ2Bib29sZWFuYCcsXG4gICAgICAgICAgICAgIGFjdHVhbDogdmFsVHlwZSxcbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgfVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIC8vIFR5cGVTY3JpcHQgdHJpY2sgZm9yIHR5cGUtZ3VhcmRpbmc6XG4gICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby11bnVzZWQtdmFyc1xuICAgICAgICAgIGNvbnN0IF86IG5ldmVyID0ga2V5XG4gICAgICAgIH1cbiAgICAgIH0pXG4gICAgfVxuXG4gICAgY29uc3QgeyBocmVmLCBhcyB9ID0gUmVhY3QudXNlTWVtbygoKSA9PiB7XG4gICAgICBpZiAoIXJvdXRlcikge1xuICAgICAgICBjb25zdCByZXNvbHZlZEhyZWYgPSBmb3JtYXRTdHJpbmdPclVybChocmVmUHJvcClcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBocmVmOiByZXNvbHZlZEhyZWYsXG4gICAgICAgICAgYXM6IGFzUHJvcCA/IGZvcm1hdFN0cmluZ09yVXJsKGFzUHJvcCkgOiByZXNvbHZlZEhyZWYsXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgY29uc3QgW3Jlc29sdmVkSHJlZiwgcmVzb2x2ZWRBc10gPSByZXNvbHZlSHJlZihyb3V0ZXIsIGhyZWZQcm9wLCB0cnVlKVxuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBocmVmOiByZXNvbHZlZEhyZWYsXG4gICAgICAgIGFzOiBhc1Byb3AgPyByZXNvbHZlSHJlZihyb3V0ZXIsIGFzUHJvcCkgOiByZXNvbHZlZEFzIHx8IHJlc29sdmVkSHJlZixcbiAgICAgIH1cbiAgICB9LCBbcm91dGVyLCBocmVmUHJvcCwgYXNQcm9wXSlcblxuICAgIGNvbnN0IHByZXZpb3VzSHJlZiA9IFJlYWN0LnVzZVJlZjxzdHJpbmc+KGhyZWYpXG4gICAgY29uc3QgcHJldmlvdXNBcyA9IFJlYWN0LnVzZVJlZjxzdHJpbmc+KGFzKVxuXG4gICAgLy8gVGhpcyB3aWxsIHJldHVybiB0aGUgZmlyc3QgY2hpbGQsIGlmIG11bHRpcGxlIGFyZSBwcm92aWRlZCBpdCB3aWxsIHRocm93IGFuIGVycm9yXG4gICAgbGV0IGNoaWxkOiBhbnlcbiAgICBpZiAobGVnYWN5QmVoYXZpb3IpIHtcbiAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50Jykge1xuICAgICAgICBpZiAob25DbGljaykge1xuICAgICAgICAgIGNvbnNvbGUud2FybihcbiAgICAgICAgICAgIGBcIm9uQ2xpY2tcIiB3YXMgcGFzc2VkIHRvIDxMaW5rPiB3aXRoIFxcYGhyZWZcXGAgb2YgXFxgJHtocmVmUHJvcH1cXGAgYnV0IFwibGVnYWN5QmVoYXZpb3JcIiB3YXMgc2V0LiBUaGUgbGVnYWN5IGJlaGF2aW9yIHJlcXVpcmVzIG9uQ2xpY2sgYmUgc2V0IG9uIHRoZSBjaGlsZCBvZiBuZXh0L2xpbmtgXG4gICAgICAgICAgKVxuICAgICAgICB9XG4gICAgICAgIGlmIChvbk1vdXNlRW50ZXJQcm9wKSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKFxuICAgICAgICAgICAgYFwib25Nb3VzZUVudGVyXCIgd2FzIHBhc3NlZCB0byA8TGluaz4gd2l0aCBcXGBocmVmXFxgIG9mIFxcYCR7aHJlZlByb3B9XFxgIGJ1dCBcImxlZ2FjeUJlaGF2aW9yXCIgd2FzIHNldC4gVGhlIGxlZ2FjeSBiZWhhdmlvciByZXF1aXJlcyBvbk1vdXNlRW50ZXIgYmUgc2V0IG9uIHRoZSBjaGlsZCBvZiBuZXh0L2xpbmtgXG4gICAgICAgICAgKVxuICAgICAgICB9XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY2hpbGQgPSBSZWFjdC5DaGlsZHJlbi5vbmx5KGNoaWxkcmVuKVxuICAgICAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgICBpZiAoIWNoaWxkcmVuKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICAgICAgIGBObyBjaGlsZHJlbiB3ZXJlIHBhc3NlZCB0byA8TGluaz4gd2l0aCBcXGBocmVmXFxgIG9mIFxcYCR7aHJlZlByb3B9XFxgIGJ1dCBvbmUgY2hpbGQgaXMgcmVxdWlyZWQgaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvbGluay1uby1jaGlsZHJlbmBcbiAgICAgICAgICAgIClcbiAgICAgICAgICB9XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICAgICAgYE11bHRpcGxlIGNoaWxkcmVuIHdlcmUgcGFzc2VkIHRvIDxMaW5rPiB3aXRoIFxcYGhyZWZcXGAgb2YgXFxgJHtocmVmUHJvcH1cXGAgYnV0IG9ubHkgb25lIGNoaWxkIGlzIHN1cHBvcnRlZCBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9saW5rLW11bHRpcGxlLWNoaWxkcmVuYCArXG4gICAgICAgICAgICAgICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJ1xuICAgICAgICAgICAgICAgID8gXCIgXFxuT3BlbiB5b3VyIGJyb3dzZXIncyBjb25zb2xlIHRvIHZpZXcgdGhlIENvbXBvbmVudCBzdGFjayB0cmFjZS5cIlxuICAgICAgICAgICAgICAgIDogJycpXG4gICAgICAgICAgKVxuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjaGlsZCA9IFJlYWN0LkNoaWxkcmVuLm9ubHkoY2hpbGRyZW4pXG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50Jykge1xuICAgICAgICBpZiAoKGNoaWxkcmVuIGFzIGFueSk/LnR5cGUgPT09ICdhJykge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICAgICAgICdJbnZhbGlkIDxMaW5rPiB3aXRoIDxhPiBjaGlsZC4gUGxlYXNlIHJlbW92ZSA8YT4gb3IgdXNlIDxMaW5rIGxlZ2FjeUJlaGF2aW9yPi5cXG5MZWFybiBtb3JlOiBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9pbnZhbGlkLW5ldy1saW5rLXdpdGgtZXh0cmEtYW5jaG9yJ1xuICAgICAgICAgIClcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIGNvbnN0IGNoaWxkUmVmOiBhbnkgPSBsZWdhY3lCZWhhdmlvclxuICAgICAgPyBjaGlsZCAmJiB0eXBlb2YgY2hpbGQgPT09ICdvYmplY3QnICYmIGNoaWxkLnJlZlxuICAgICAgOiBmb3J3YXJkZWRSZWZcblxuICAgIGNvbnN0IFtzZXRJbnRlcnNlY3Rpb25SZWYsIGlzVmlzaWJsZSwgcmVzZXRWaXNpYmxlXSA9IHVzZUludGVyc2VjdGlvbih7XG4gICAgICByb290TWFyZ2luOiAnMjAwcHgnLFxuICAgIH0pXG5cbiAgICBjb25zdCBzZXRJbnRlcnNlY3Rpb25XaXRoUmVzZXRSZWYgPSBSZWFjdC51c2VDYWxsYmFjayhcbiAgICAgIChlbDogRWxlbWVudCB8IG51bGwpID0+IHtcbiAgICAgICAgLy8gQmVmb3JlIHRoZSBsaW5rIGdldHRpbmcgb2JzZXJ2ZWQsIGNoZWNrIGlmIHZpc2libGUgc3RhdGUgbmVlZCB0byBiZSByZXNldFxuICAgICAgICBpZiAocHJldmlvdXNBcy5jdXJyZW50ICE9PSBhcyB8fCBwcmV2aW91c0hyZWYuY3VycmVudCAhPT0gaHJlZikge1xuICAgICAgICAgIHJlc2V0VmlzaWJsZSgpXG4gICAgICAgICAgcHJldmlvdXNBcy5jdXJyZW50ID0gYXNcbiAgICAgICAgICBwcmV2aW91c0hyZWYuY3VycmVudCA9IGhyZWZcbiAgICAgICAgfVxuXG4gICAgICAgIHNldEludGVyc2VjdGlvblJlZihlbClcbiAgICAgIH0sXG4gICAgICBbYXMsIGhyZWYsIHJlc2V0VmlzaWJsZSwgc2V0SW50ZXJzZWN0aW9uUmVmXVxuICAgIClcblxuICAgIGNvbnN0IHNldFJlZiA9IHVzZU1lcmdlZFJlZihzZXRJbnRlcnNlY3Rpb25XaXRoUmVzZXRSZWYsIGNoaWxkUmVmKVxuXG4gICAgLy8gUHJlZmV0Y2ggdGhlIFVSTCBpZiB3ZSBoYXZlbid0IGFscmVhZHkgYW5kIGl0J3MgdmlzaWJsZS5cbiAgICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgLy8gaW4gZGV2LCB3ZSBvbmx5IHByZWZldGNoIG9uIGhvdmVyIHRvIGF2b2lkIHdhc3RpbmcgcmVzb3VyY2VzIGFzIHRoZSBwcmVmZXRjaCB3aWxsIHRyaWdnZXIgY29tcGlsaW5nIHRoZSBwYWdlLlxuICAgICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG5cbiAgICAgIGlmICghcm91dGVyKSB7XG4gICAgICAgIHJldHVyblxuICAgICAgfVxuXG4gICAgICAvLyBJZiB3ZSBkb24ndCBuZWVkIHRvIHByZWZldGNoIHRoZSBVUkwsIGRvbid0IGRvIHByZWZldGNoLlxuICAgICAgaWYgKCFpc1Zpc2libGUgfHwgIXByZWZldGNoRW5hYmxlZCkge1xuICAgICAgICByZXR1cm5cbiAgICAgIH1cblxuICAgICAgLy8gUHJlZmV0Y2ggdGhlIFVSTC5cbiAgICAgIHByZWZldGNoKHJvdXRlciwgaHJlZiwgYXMsIHsgbG9jYWxlIH0pXG4gICAgfSwgW2FzLCBocmVmLCBpc1Zpc2libGUsIGxvY2FsZSwgcHJlZmV0Y2hFbmFibGVkLCByb3V0ZXI/LmxvY2FsZSwgcm91dGVyXSlcblxuICAgIGNvbnN0IGNoaWxkUHJvcHM6IHtcbiAgICAgIG9uVG91Y2hTdGFydD86IFJlYWN0LlRvdWNoRXZlbnRIYW5kbGVyPEhUTUxBbmNob3JFbGVtZW50PlxuICAgICAgb25Nb3VzZUVudGVyOiBSZWFjdC5Nb3VzZUV2ZW50SGFuZGxlcjxIVE1MQW5jaG9yRWxlbWVudD5cbiAgICAgIG9uQ2xpY2s6IFJlYWN0Lk1vdXNlRXZlbnRIYW5kbGVyPEhUTUxBbmNob3JFbGVtZW50PlxuICAgICAgaHJlZj86IHN0cmluZ1xuICAgICAgcmVmPzogYW55XG4gICAgfSA9IHtcbiAgICAgIHJlZjogc2V0UmVmLFxuICAgICAgb25DbGljayhlKSB7XG4gICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgICAgICAgaWYgKCFlKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICAgICAgIGBDb21wb25lbnQgcmVuZGVyZWQgaW5zaWRlIG5leHQvbGluayBoYXMgdG8gcGFzcyBjbGljayBldmVudCB0byBcIm9uQ2xpY2tcIiBwcm9wLmBcbiAgICAgICAgICAgIClcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICBpZiAoIWxlZ2FjeUJlaGF2aW9yICYmIHR5cGVvZiBvbkNsaWNrID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICAgb25DbGljayhlKVxuICAgICAgICB9XG5cbiAgICAgICAgaWYgKFxuICAgICAgICAgIGxlZ2FjeUJlaGF2aW9yICYmXG4gICAgICAgICAgY2hpbGQucHJvcHMgJiZcbiAgICAgICAgICB0eXBlb2YgY2hpbGQucHJvcHMub25DbGljayA9PT0gJ2Z1bmN0aW9uJ1xuICAgICAgICApIHtcbiAgICAgICAgICBjaGlsZC5wcm9wcy5vbkNsaWNrKGUpXG4gICAgICAgIH1cblxuICAgICAgICBpZiAoIXJvdXRlcikge1xuICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG5cbiAgICAgICAgaWYgKGUuZGVmYXVsdFByZXZlbnRlZCkge1xuICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG5cbiAgICAgICAgbGlua0NsaWNrZWQoXG4gICAgICAgICAgZSxcbiAgICAgICAgICByb3V0ZXIsXG4gICAgICAgICAgaHJlZixcbiAgICAgICAgICBhcyxcbiAgICAgICAgICByZXBsYWNlLFxuICAgICAgICAgIHNoYWxsb3csXG4gICAgICAgICAgc2Nyb2xsLFxuICAgICAgICAgIGxvY2FsZSxcbiAgICAgICAgICBvbk5hdmlnYXRlXG4gICAgICAgIClcbiAgICAgIH0sXG4gICAgICBvbk1vdXNlRW50ZXIoZSkge1xuICAgICAgICBpZiAoIWxlZ2FjeUJlaGF2aW9yICYmIHR5cGVvZiBvbk1vdXNlRW50ZXJQcm9wID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICAgb25Nb3VzZUVudGVyUHJvcChlKVxuICAgICAgICB9XG5cbiAgICAgICAgaWYgKFxuICAgICAgICAgIGxlZ2FjeUJlaGF2aW9yICYmXG4gICAgICAgICAgY2hpbGQucHJvcHMgJiZcbiAgICAgICAgICB0eXBlb2YgY2hpbGQucHJvcHMub25Nb3VzZUVudGVyID09PSAnZnVuY3Rpb24nXG4gICAgICAgICkge1xuICAgICAgICAgIGNoaWxkLnByb3BzLm9uTW91c2VFbnRlcihlKVxuICAgICAgICB9XG5cbiAgICAgICAgaWYgKCFyb3V0ZXIpIHtcbiAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuXG4gICAgICAgIHByZWZldGNoKHJvdXRlciwgaHJlZiwgYXMsIHtcbiAgICAgICAgICBsb2NhbGUsXG4gICAgICAgICAgcHJpb3JpdHk6IHRydWUsXG4gICAgICAgICAgLy8gQHNlZSB7aHR0cHM6Ly9naXRodWIuY29tL3ZlcmNlbC9uZXh0LmpzL2Rpc2N1c3Npb25zLzQwMjY4P3NvcnQ9dG9wI2Rpc2N1c3Npb25jb21tZW50LTM1NzI2NDJ9XG4gICAgICAgICAgYnlwYXNzUHJlZmV0Y2hlZENoZWNrOiB0cnVlLFxuICAgICAgICB9KVxuICAgICAgfSxcbiAgICAgIG9uVG91Y2hTdGFydDogcHJvY2Vzcy5lbnYuX19ORVhUX0xJTktfTk9fVE9VQ0hfU1RBUlRcbiAgICAgICAgPyB1bmRlZmluZWRcbiAgICAgICAgOiBmdW5jdGlvbiBvblRvdWNoU3RhcnQoZSkge1xuICAgICAgICAgICAgaWYgKCFsZWdhY3lCZWhhdmlvciAmJiB0eXBlb2Ygb25Ub3VjaFN0YXJ0UHJvcCA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgICBvblRvdWNoU3RhcnRQcm9wKGUpXG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGlmIChcbiAgICAgICAgICAgICAgbGVnYWN5QmVoYXZpb3IgJiZcbiAgICAgICAgICAgICAgY2hpbGQucHJvcHMgJiZcbiAgICAgICAgICAgICAgdHlwZW9mIGNoaWxkLnByb3BzLm9uVG91Y2hTdGFydCA9PT0gJ2Z1bmN0aW9uJ1xuICAgICAgICAgICAgKSB7XG4gICAgICAgICAgICAgIGNoaWxkLnByb3BzLm9uVG91Y2hTdGFydChlKVxuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBpZiAoIXJvdXRlcikge1xuICAgICAgICAgICAgICByZXR1cm5cbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgcHJlZmV0Y2gocm91dGVyLCBocmVmLCBhcywge1xuICAgICAgICAgICAgICBsb2NhbGUsXG4gICAgICAgICAgICAgIHByaW9yaXR5OiB0cnVlLFxuICAgICAgICAgICAgICAvLyBAc2VlIHtodHRwczovL2dpdGh1Yi5jb20vdmVyY2VsL25leHQuanMvZGlzY3Vzc2lvbnMvNDAyNjg/c29ydD10b3AjZGlzY3Vzc2lvbmNvbW1lbnQtMzU3MjY0Mn1cbiAgICAgICAgICAgICAgYnlwYXNzUHJlZmV0Y2hlZENoZWNrOiB0cnVlLFxuICAgICAgICAgICAgfSlcbiAgICAgICAgICB9LFxuICAgIH1cblxuICAgIC8vIElmIGNoaWxkIGlzIGFuIDxhPiB0YWcgYW5kIGRvZXNuJ3QgaGF2ZSBhIGhyZWYgYXR0cmlidXRlLCBvciBpZiB0aGUgJ3Bhc3NIcmVmJyBwcm9wZXJ0eSBpc1xuICAgIC8vIGRlZmluZWQsIHdlIHNwZWNpZnkgdGhlIGN1cnJlbnQgJ2hyZWYnLCBzbyB0aGF0IHJlcGV0aXRpb24gaXMgbm90IG5lZWRlZCBieSB0aGUgdXNlci5cbiAgICAvLyBJZiB0aGUgdXJsIGlzIGFic29sdXRlLCB3ZSBjYW4gYnlwYXNzIHRoZSBsb2dpYyB0byBwcmVwZW5kIHRoZSBkb21haW4gYW5kIGxvY2FsZS5cbiAgICBpZiAoaXNBYnNvbHV0ZVVybChhcykpIHtcbiAgICAgIGNoaWxkUHJvcHMuaHJlZiA9IGFzXG4gICAgfSBlbHNlIGlmIChcbiAgICAgICFsZWdhY3lCZWhhdmlvciB8fFxuICAgICAgcGFzc0hyZWYgfHxcbiAgICAgIChjaGlsZC50eXBlID09PSAnYScgJiYgISgnaHJlZicgaW4gY2hpbGQucHJvcHMpKVxuICAgICkge1xuICAgICAgY29uc3QgY3VyTG9jYWxlID0gdHlwZW9mIGxvY2FsZSAhPT0gJ3VuZGVmaW5lZCcgPyBsb2NhbGUgOiByb3V0ZXI/LmxvY2FsZVxuXG4gICAgICAvLyB3ZSBvbmx5IHJlbmRlciBkb21haW4gbG9jYWxlcyBpZiB3ZSBhcmUgY3VycmVudGx5IG9uIGEgZG9tYWluIGxvY2FsZVxuICAgICAgLy8gc28gdGhhdCBsb2NhbGUgbGlua3MgYXJlIHN0aWxsIHZpc2l0YWJsZSBpbiBkZXZlbG9wbWVudC9wcmV2aWV3IGVudnNcbiAgICAgIGNvbnN0IGxvY2FsZURvbWFpbiA9XG4gICAgICAgIHJvdXRlcj8uaXNMb2NhbGVEb21haW4gJiZcbiAgICAgICAgZ2V0RG9tYWluTG9jYWxlKGFzLCBjdXJMb2NhbGUsIHJvdXRlcj8ubG9jYWxlcywgcm91dGVyPy5kb21haW5Mb2NhbGVzKVxuXG4gICAgICBjaGlsZFByb3BzLmhyZWYgPVxuICAgICAgICBsb2NhbGVEb21haW4gfHxcbiAgICAgICAgYWRkQmFzZVBhdGgoYWRkTG9jYWxlKGFzLCBjdXJMb2NhbGUsIHJvdXRlcj8uZGVmYXVsdExvY2FsZSkpXG4gICAgfVxuXG4gICAgaWYgKGxlZ2FjeUJlaGF2aW9yKSB7XG4gICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHtcbiAgICAgICAgZXJyb3JPbmNlKFxuICAgICAgICAgICdgbGVnYWN5QmVoYXZpb3JgIGlzIGRlcHJlY2F0ZWQgYW5kIHdpbGwgYmUgcmVtb3ZlZCBpbiBhIGZ1dHVyZSAnICtcbiAgICAgICAgICAgICdyZWxlYXNlLiBBIGNvZGVtb2QgaXMgYXZhaWxhYmxlIHRvIHVwZ3JhZGUgeW91ciBjb21wb25lbnRzOlxcblxcbicgK1xuICAgICAgICAgICAgJ25weCBAbmV4dC9jb2RlbW9kQGxhdGVzdCBuZXctbGluayAuXFxuXFxuJyArXG4gICAgICAgICAgICAnTGVhcm4gbW9yZTogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2J1aWxkaW5nLXlvdXItYXBwbGljYXRpb24vdXBncmFkaW5nL2NvZGVtb2RzI3JlbW92ZS1hLXRhZ3MtZnJvbS1saW5rLWNvbXBvbmVudHMnXG4gICAgICAgIClcbiAgICAgIH1cbiAgICAgIHJldHVybiBSZWFjdC5jbG9uZUVsZW1lbnQoY2hpbGQsIGNoaWxkUHJvcHMpXG4gICAgfVxuXG4gICAgcmV0dXJuIChcbiAgICAgIDxhIHsuLi5yZXN0UHJvcHN9IHsuLi5jaGlsZFByb3BzfT5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9hPlxuICAgIClcbiAgfVxuKVxuXG5jb25zdCBMaW5rU3RhdHVzQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8e1xuICBwZW5kaW5nOiBib29sZWFuXG59Pih7XG4gIC8vIFdlIGRvIG5vdCBzdXBwb3J0IGxpbmsgc3RhdHVzIGluIHRoZSBQYWdlcyBSb3V0ZXIsIHNvIHdlIGFsd2F5cyByZXR1cm4gZmFsc2VcbiAgcGVuZGluZzogZmFsc2UsXG59KVxuXG5leHBvcnQgY29uc3QgdXNlTGlua1N0YXR1cyA9ICgpID0+IHtcbiAgLy8gVGhpcyBiZWhhdmlvdXIgaXMgbGlrZSBSZWFjdCdzIHVzZUZvcm1TdGF0dXMuIFdoZW4gdGhlIGNvbXBvbmVudCBpcyBub3QgdW5kZXJcbiAgLy8gYSA8Zm9ybT4gdGFnLCBpdCB3aWxsIGdldCB0aGUgZGVmYXVsdCB2YWx1ZSwgaW5zdGVhZCBvZiB0aHJvd2luZyBhbiBlcnJvci5cbiAgcmV0dXJuIHVzZUNvbnRleHQoTGlua1N0YXR1c0NvbnRleHQpXG59XG5cbmV4cG9ydCBkZWZhdWx0IExpbmtcbiJdLCJuYW1lcyI6WyJ1c2VMaW5rU3RhdHVzIiwicHJlZmV0Y2hlZCIsIlNldCIsInByZWZldGNoIiwicm91dGVyIiwiaHJlZiIsImFzIiwib3B0aW9ucyIsIndpbmRvdyIsImlzTG9jYWxVUkwiLCJieXBhc3NQcmVmZXRjaGVkQ2hlY2siLCJsb2NhbGUiLCJ1bmRlZmluZWQiLCJwcmVmZXRjaGVkS2V5IiwiaGFzIiwiYWRkIiwiY2F0Y2giLCJlcnIiLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJpc01vZGlmaWVkRXZlbnQiLCJldmVudCIsImV2ZW50VGFyZ2V0IiwiY3VycmVudFRhcmdldCIsInRhcmdldCIsImdldEF0dHJpYnV0ZSIsIm1ldGFLZXkiLCJjdHJsS2V5Iiwic2hpZnRLZXkiLCJhbHRLZXkiLCJuYXRpdmVFdmVudCIsIndoaWNoIiwibGlua0NsaWNrZWQiLCJlIiwicmVwbGFjZSIsInNoYWxsb3ciLCJzY3JvbGwiLCJvbk5hdmlnYXRlIiwibm9kZU5hbWUiLCJpc0FuY2hvck5vZGVOYW1lIiwidG9VcHBlckNhc2UiLCJoYXNBdHRyaWJ1dGUiLCJwcmV2ZW50RGVmYXVsdCIsImxvY2F0aW9uIiwibmF2aWdhdGUiLCJpc0RlZmF1bHRQcmV2ZW50ZWQiLCJyb3V0ZXJTY3JvbGwiLCJmb3JtYXRTdHJpbmdPclVybCIsInVybE9iak9yU3RyaW5nIiwiZm9ybWF0VXJsIiwiTGluayIsIlJlYWN0IiwiZm9yd2FyZFJlZiIsIkxpbmtDb21wb25lbnQiLCJwcm9wcyIsImZvcndhcmRlZFJlZiIsImNoaWxkcmVuIiwiaHJlZlByb3AiLCJhc1Byb3AiLCJjaGlsZHJlblByb3AiLCJwcmVmZXRjaFByb3AiLCJwYXNzSHJlZiIsIm9uQ2xpY2siLCJvbk1vdXNlRW50ZXIiLCJvbk1vdXNlRW50ZXJQcm9wIiwib25Ub3VjaFN0YXJ0Iiwib25Ub3VjaFN0YXJ0UHJvcCIsImxlZ2FjeUJlaGF2aW9yIiwicmVzdFByb3BzIiwiYSIsInVzZUNvbnRleHQiLCJSb3V0ZXJDb250ZXh0IiwicHJlZmV0Y2hFbmFibGVkIiwiY3JlYXRlUHJvcEVycm9yIiwiYXJncyIsIkVycm9yIiwia2V5IiwiZXhwZWN0ZWQiLCJhY3R1YWwiLCJyZXF1aXJlZFByb3BzR3VhcmQiLCJyZXF1aXJlZFByb3BzIiwiT2JqZWN0Iiwia2V5cyIsImZvckVhY2giLCJfIiwib3B0aW9uYWxQcm9wc0d1YXJkIiwib3B0aW9uYWxQcm9wcyIsInZhbFR5cGUiLCJ1c2VNZW1vIiwicmVzb2x2ZWRIcmVmIiwicmVzb2x2ZWRBcyIsInJlc29sdmVIcmVmIiwicHJldmlvdXNIcmVmIiwidXNlUmVmIiwicHJldmlvdXNBcyIsImNoaWxkIiwiY29uc29sZSIsIndhcm4iLCJDaGlsZHJlbiIsIm9ubHkiLCJ0eXBlIiwiY2hpbGRSZWYiLCJyZWYiLCJzZXRJbnRlcnNlY3Rpb25SZWYiLCJpc1Zpc2libGUiLCJyZXNldFZpc2libGUiLCJ1c2VJbnRlcnNlY3Rpb24iLCJyb290TWFyZ2luIiwic2V0SW50ZXJzZWN0aW9uV2l0aFJlc2V0UmVmIiwidXNlQ2FsbGJhY2siLCJlbCIsImN1cnJlbnQiLCJzZXRSZWYiLCJ1c2VNZXJnZWRSZWYiLCJ1c2VFZmZlY3QiLCJjaGlsZFByb3BzIiwiZGVmYXVsdFByZXZlbnRlZCIsInByaW9yaXR5IiwiX19ORVhUX0xJTktfTk9fVE9VQ0hfU1RBUlQiLCJpc0Fic29sdXRlVXJsIiwiY3VyTG9jYWxlIiwibG9jYWxlRG9tYWluIiwiaXNMb2NhbGVEb21haW4iLCJnZXREb21haW5Mb2NhbGUiLCJsb2NhbGVzIiwiZG9tYWluTG9jYWxlcyIsImFkZEJhc2VQYXRoIiwiYWRkTG9jYWxlIiwiZGVmYXVsdExvY2FsZSIsImVycm9yT25jZSIsImNsb25lRWxlbWVudCIsIkxpbmtTdGF0dXNDb250ZXh0IiwiY3JlYXRlQ29udGV4dCIsInBlbmRpbmciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/link.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/use-intersection.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/use-intersection.js ***!
  \***********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useIntersection\", ({\n    enumerable: true,\n    get: function() {\n        return useIntersection;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nconst _requestidlecallback = __webpack_require__(/*! ./request-idle-callback */ \"(pages-dir-browser)/./node_modules/next/dist/client/request-idle-callback.js\");\nconst hasIntersectionObserver = typeof IntersectionObserver === 'function';\nconst observers = new Map();\nconst idList = [];\nfunction createObserver(options) {\n    const id = {\n        root: options.root || null,\n        margin: options.rootMargin || ''\n    };\n    const existing = idList.find((obj)=>obj.root === id.root && obj.margin === id.margin);\n    let instance;\n    if (existing) {\n        instance = observers.get(existing);\n        if (instance) {\n            return instance;\n        }\n    }\n    const elements = new Map();\n    const observer = new IntersectionObserver((entries)=>{\n        entries.forEach((entry)=>{\n            const callback = elements.get(entry.target);\n            const isVisible = entry.isIntersecting || entry.intersectionRatio > 0;\n            if (callback && isVisible) {\n                callback(isVisible);\n            }\n        });\n    }, options);\n    instance = {\n        id,\n        observer,\n        elements\n    };\n    idList.push(id);\n    observers.set(id, instance);\n    return instance;\n}\nfunction observe(element, callback, options) {\n    const { id, observer, elements } = createObserver(options);\n    elements.set(element, callback);\n    observer.observe(element);\n    return function unobserve() {\n        elements.delete(element);\n        observer.unobserve(element);\n        // Destroy observer when there's nothing left to watch:\n        if (elements.size === 0) {\n            observer.disconnect();\n            observers.delete(id);\n            const index = idList.findIndex((obj)=>obj.root === id.root && obj.margin === id.margin);\n            if (index > -1) {\n                idList.splice(index, 1);\n            }\n        }\n    };\n}\nfunction useIntersection(param) {\n    let { rootRef, rootMargin, disabled } = param;\n    const isDisabled = disabled || !hasIntersectionObserver;\n    const [visible, setVisible] = (0, _react.useState)(false);\n    const elementRef = (0, _react.useRef)(null);\n    const setElement = (0, _react.useCallback)((element)=>{\n        elementRef.current = element;\n    }, []);\n    (0, _react.useEffect)(()=>{\n        if (hasIntersectionObserver) {\n            if (isDisabled || visible) return;\n            const element = elementRef.current;\n            if (element && element.tagName) {\n                const unobserve = observe(element, (isVisible)=>isVisible && setVisible(isVisible), {\n                    root: rootRef == null ? void 0 : rootRef.current,\n                    rootMargin\n                });\n                return unobserve;\n            }\n        } else {\n            if (!visible) {\n                const idleCallback = (0, _requestidlecallback.requestIdleCallback)(()=>setVisible(true));\n                return ()=>(0, _requestidlecallback.cancelIdleCallback)(idleCallback);\n            }\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        isDisabled,\n        rootMargin,\n        rootRef,\n        visible,\n        elementRef.current\n    ]);\n    const resetVisible = (0, _react.useCallback)(()=>{\n        setVisible(false);\n    }, []);\n    return [\n        setElement,\n        visible,\n        resetVisible\n    ];\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-intersection.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/use-intersection.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/use-merged-ref.js":
/*!*********************************************************!*\
  !*** ./node_modules/next/dist/client/use-merged-ref.js ***!
  \*********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useMergedRef\", ({\n    enumerable: true,\n    get: function() {\n        return useMergedRef;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nfunction useMergedRef(refA, refB) {\n    const cleanupA = (0, _react.useRef)(null);\n    const cleanupB = (0, _react.useRef)(null);\n    // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n    // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n    // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n    // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n    // (because it hasn't been updated for React 19)\n    // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n    // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n    return (0, _react.useCallback)((current)=>{\n        if (current === null) {\n            const cleanupFnA = cleanupA.current;\n            if (cleanupFnA) {\n                cleanupA.current = null;\n                cleanupFnA();\n            }\n            const cleanupFnB = cleanupB.current;\n            if (cleanupFnB) {\n                cleanupB.current = null;\n                cleanupFnB();\n            }\n        } else {\n            if (refA) {\n                cleanupA.current = applyRef(refA, current);\n            }\n            if (refB) {\n                cleanupB.current = applyRef(refB, current);\n            }\n        }\n    }, [\n        refA,\n        refB\n    ]);\n}\nfunction applyRef(refA, current) {\n    if (typeof refA === 'function') {\n        const cleanup = refA(current);\n        if (typeof cleanup === 'function') {\n            return cleanup;\n        } else {\n            return ()=>refA(null);\n        }\n    } else {\n        refA.current = current;\n        return ()=>{\n            refA.current = null;\n        };\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-merged-ref.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/use-merged-ref.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js":
/*!***************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils/error-once.js ***!
  \***************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"errorOnce\", ({\n    enumerable: true,\n    get: function() {\n        return errorOnce;\n    }\n}));\nlet errorOnce = (_)=>{};\nif (true) {\n    const errors = new Set();\n    errorOnce = (msg)=>{\n        if (!errors.has(msg)) {\n            console.error(msg);\n        }\n        errors.add(msg);\n    };\n} //# sourceMappingURL=error-once.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi91dGlscy9lcnJvci1vbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7NkNBV1NBOzs7ZUFBQUE7OztBQVhULElBQUlBLFlBQVksQ0FBQ0MsS0FBZTtBQUNoQyxJQUFJQyxJQUFvQixFQUFtQjtJQUN6QyxNQUFNRyxTQUFTLElBQUlDO0lBQ25CTixZQUFZLENBQUNPO1FBQ1gsSUFBSSxDQUFDRixPQUFPRyxHQUFHLENBQUNELE1BQU07WUFDcEJFLFFBQVFDLEtBQUssQ0FBQ0g7UUFDaEI7UUFDQUYsT0FBT00sR0FBRyxDQUFDSjtJQUNiO0FBQ0YiLCJzb3VyY2VzIjpbIi9zcmMvc2hhcmVkL2xpYi91dGlscy9lcnJvci1vbmNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImxldCBlcnJvck9uY2UgPSAoXzogc3RyaW5nKSA9PiB7fVxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgY29uc3QgZXJyb3JzID0gbmV3IFNldDxzdHJpbmc+KClcbiAgZXJyb3JPbmNlID0gKG1zZzogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFlcnJvcnMuaGFzKG1zZykpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IobXNnKVxuICAgIH1cbiAgICBlcnJvcnMuYWRkKG1zZylcbiAgfVxufVxuXG5leHBvcnQgeyBlcnJvck9uY2UgfVxuIl0sIm5hbWVzIjpbImVycm9yT25jZSIsIl8iLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJlcnJvcnMiLCJTZXQiLCJtc2ciLCJoYXMiLCJjb25zb2xlIiwiZXJyb3IiLCJhZGQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/link.js":
/*!***********************************!*\
  !*** ./node_modules/next/link.js ***!
  \***********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/link */ \"(pages-dir-browser)/./node_modules/next/dist/client/link.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2xpbmsuanMiLCJtYXBwaW5ncyI6IkFBQUEsNkhBQThDIiwic291cmNlcyI6WyIvaG9tZS90aW1vL0Rldi9yZXZpZXdlcmluYXRvci9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kaXN0L2NsaWVudC9saW5rJylcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/link.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(pages-dir-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction Home() {\n    _s();\n    const [googleUrl, setGoogleUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        setIsLoading(true);\n        try {\n            // Validate URL\n            if (!googleUrl.includes('google.com/maps') && !googleUrl.includes('goo.gl')) {\n                throw new Error('Please enter a valid Google Maps URL');\n            }\n            // Call scraping API\n            const response = await fetch('/api/scrape-reviews', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    googleUrl\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to scrape reviews');\n            }\n            // Redirect to report page\n            router.push(\"/report/\".concat(data.reportId));\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'An error occurred');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    children: \"Reviewerinator - AI Google Reviews Analysis\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"container mx-auto px-4 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                href: \"/dashboard\",\n                                className: \"text-primary-600 hover:text-primary-700 font-medium\",\n                                children: \"View Dashboard →\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4 py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-5xl font-bold text-gray-900 mb-4\",\n                                        children: \"Reviewerinator\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                                        children: \"Transform Google Reviews into actionable business insights with AI-powered analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-2xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"googleUrl\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Google Maps Business URL\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                            lineNumber: 79,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"url\",\n                                                            id: \"googleUrl\",\n                                                            value: googleUrl,\n                                                            onChange: (e)=>setGoogleUrl(e.target.value),\n                                                            placeholder: \"https://maps.google.com/...\",\n                                                            className: \"input-field\",\n                                                            required: true,\n                                                            disabled: isLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                            lineNumber: 82,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 mt-1\",\n                                                            children: \"Paste the Google Maps URL of the business you want to analyze\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                            lineNumber: 92,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 17\n                                                }, this),\n                                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    disabled: isLoading || !googleUrl.trim(),\n                                                    className: \"w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                                lineNumber: 110,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Analyzing Reviews...\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                        lineNumber: 109,\n                                                        columnNumber: 21\n                                                    }, this) : 'Generate AI Report'\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-16 grid md:grid-cols-3 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-8 h-8 text-primary-600\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                        children: \"Smart Analytics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"AI-powered sentiment analysis and keyword extraction from customer reviews\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-8 h-8 text-primary-600\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                        children: \"Visual Insights\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"Interactive charts and graphs to visualize customer sentiment trends\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-8 h-8 text-primary-600\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                                lineNumber: 145,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                        children: \"Actionable Reports\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"Detailed recommendations to improve your business based on customer feedback\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Dev/reviewerinator/pages/index.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"3IkKuuEcFYa/dtUI7dKRELur6iM=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/index.tsx\n"));

/***/ })

});