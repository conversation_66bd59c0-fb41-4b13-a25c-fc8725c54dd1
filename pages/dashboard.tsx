import { GetServerSideProps } from 'next';
import Head from 'next/head';
import Link from 'next/link';
import { prisma } from '@/lib/database';
import { Report, Business } from '@/types';

interface DashboardProps {
  reports: (Report & { business: Business })[];
}

export default function Dashboard({ reports }: DashboardProps) {
  return (
    <>
      <Head>
        <title>Dashboard - Reviewerinator</title>
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
                <p className="text-gray-600">Recent analysis reports</p>
              </div>
              <Link href="/" className="btn-primary">
                New Analysis
              </Link>
            </div>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          {reports.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No reports yet</h3>
              <p className="text-gray-600 mb-4">Create your first analysis report to get started</p>
              <Link href="/" className="btn-primary">
                Create Report
              </Link>
            </div>
          ) : (
            <div className="grid gap-6">
              {reports.map((report) => {
                const insights = JSON.parse(report.insights);
                const sentimentData = JSON.parse(report.sentimentData);
                
                return (
                  <div key={report.id} className="card hover:shadow-lg transition-shadow">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">
                          {report.business.name}
                        </h3>
                        <p className="text-gray-600 text-sm mb-3">
                          {report.business.address}
                        </p>
                        <p className="text-gray-700 mb-4 line-clamp-2">
                          {report.summary.substring(0, 200)}...
                        </p>
                        <div className="flex items-center space-x-6 text-sm">
                          <div className="flex items-center">
                            <span className="text-yellow-500 mr-1">★</span>
                            <span className="font-medium">{insights.averageRating}/5</span>
                          </div>
                          <div className="text-gray-500">
                            {insights.totalReviews} reviews
                          </div>
                          <div className="text-green-600">
                            {sentimentData.positive}% positive
                          </div>
                          <div className="text-gray-500">
                            {new Date(report.createdAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <div className="ml-6 flex flex-col items-end">
                        <div className="text-2xl font-bold text-primary-600 mb-2">
                          {insights.averageRating}/5
                        </div>
                        <Link
                          href={`/report/${report.id}`}
                          className="btn-primary text-sm"
                        >
                          View Report
                        </Link>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </>
  );
}

export const getServerSideProps: GetServerSideProps = async () => {
  try {
    const reports = await prisma.report.findMany({
      include: {
        business: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 20, // Limit to latest 20 reports
    });

    return {
      props: {
        reports: reports.map(report => ({
          ...report,
          createdAt: report.createdAt.toISOString(),
          business: {
            ...report.business,
            createdAt: report.business.createdAt.toISOString(),
            updatedAt: report.business.updatedAt.toISOString(),
          },
        })),
      },
    };
  } catch (error) {
    console.error('Error fetching reports:', error);
    return {
      props: {
        reports: [],
      },
    };
  }
};
